<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Hash;

class Student extends Authenticatable
{
    use HasApiTokens, Notifiable, HasFactory;

    protected $fillable = [
        'profile_photo',
        'arabic_name',
        'english_name',
        'gender',
        'dob',
        'email',
        'password',
        'age',
        'parent_number',
        'level_of_player',
        'position',
        'category',
        'weight',
        'height',
        'school_name',
        'instagram',
        'facebook',
        'active',
        'balance',
        'registration_date',
    ];

    protected $casts = [
        'dob' => 'date',
        'registration_date' => 'date',
        'active' => 'boolean',
        'balance' => 'decimal:2',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($student) {
            if (isset($student->password) && !password_get_info($student->password)['algo']) {
                $student->password = Hash::make($student->password);
            }
        });

        static::updating(function ($student) {
            if ($student->isDirty('password') && !password_get_info($student->password)['algo']) {
                $student->password = Hash::make($student->password);
            }
        });
    }

    public function classes(): BelongsToMany
    {
        return $this->belongsToMany(ClassModel::class, 'class_enrollments', 'student_id', 'class_id')
                    ->withPivot('enrollment_date', 'is_active')
                    ->withTimestamps();
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function coach()
{
    return $this->belongsTo(User::class, 'coach_id');
}
    public function enrollInClass(ClassModel $class)
    {
        DB::transaction(function () use ($class) {
            $this->balance -= $class->price_per_month;
            $this->save();

            $this->classes()->attach($class->id, [
                'enrollment_date' => now(),
                'is_active' => true,
            ]);

            $this->payments()->create([
                'class_id' => $class->id,
                'amount' => $class->price_per_month,
                'payment_method' => 'pending',
                'payment_date' => now(),
                'notes' => 'Enrollment fee'
            ]);
        });
    }
}