/* Styles for the error page container */
#error-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: auto;
    font-family: Arial, sans-serif;
    /* background-color: #000000; */
    padding: 20px;
  }
  
  .error-container {
    text-align: center;
    max-width: 600px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .error-container h1 {
    color: #ff6347; /* Reddish color for the header */
    font-size: 36px;
    margin-bottom: 15px;
  }
  
  .error-container p {
    color: #333;
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 10px;
  }
  
  .error-container a {
    color: #007bff; /* Blue color for the link */
    text-decoration: none;
    border-bottom: 1px solid #007bff;
    transition: border-bottom 0.3s ease-in-out;
  }
  
  .error-container a:hover {
    border-bottom: 2px solid #007bff;
  }
  
  .error-gif {
    margin-top: 20px;
  }
  
  /* Style for the iframe */
  .error-gif iframe {
    border: none;
    border-radius: 8px;
    overflow: hidden;
    max-width: 100%;
  }
  
  /* Media query for smaller screens */
  @media screen and (max-width: 600px) {
    .error-container {
      max-width: 90%;
    }
  }
  

  /* -----------------------------  */

  .image-container {
    max-width: 100%;
    height: auto;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  
  .responsive-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
  }
  body {
    background-color: #F5FEFF;
  }

  .error-gif {
    margin: 1rem 3rem;
  }
  
  /* Style for the iframe */
  .error-gif iframe {
    border: none;
    border-radius: 8px;
    overflow: hidden;
    max-width: 100%;
  }
  