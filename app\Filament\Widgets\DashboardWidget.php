<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use App\Models\Student;
use App\Models\User;
use App\Models\Payment;
use App\Models\Attendance;
use App\Models\ClassModel;
use App\Models\ClassEnrollment;

class DashboardWidget extends Widget
{
    protected static string $view = 'filament.widgets.dashboard-widget';

    public function getStudentsCount(): int
    {
        return Student::count();
    }

    public function getUsersCount(): int
    {
        return User::count();
    }

    public function getPaymentsCount(): int
    {
        return Payment::count();
    }

    public function getAttendanceCount(): int
    {
        return Attendance::count();
    }

    public function getClassesCount(): int
    {
        return ClassModel::count();
    }

    public function getClassEnrollmentsCount(): int
    {
        return ClassEnrollment::count();
    }
} 