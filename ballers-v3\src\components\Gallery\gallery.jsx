import React from "react";
import ImageGallery from "react-image-gallery";
import { photos } from "./photos";
import "react-image-gallery/styles/css/image-gallery.css";
import "./gallery.css";

export default function CustomImageGallery() {
  const images = photos.map((photo) => ({
    original: photo.src,
    thumbnail: photo.src,
    description: photo.title || "", // optional
  }));

  return (
    <div className="gallery-container">
      <ImageGallery items={images} showPlayButton={false} showFullscreenButton={true} />
    </div>
  );
}
