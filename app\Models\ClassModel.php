<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClassModel extends Model
{
    use HasFactory;

    protected $fillable = [
        'class_name',
        'description',
        'day_of_week',
        'start_time',
        'end_time',
        'instructor_id', // Changed to instructor_id (FK to User)
        'capacity',
        'price_per_month'
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i', // Cast to time
        'end_time' => 'datetime:H:i',
        'price_per_month' => 'decimal:2',
    ];

    public function students(): BelongsToMany
    {
    return $this->belongsToMany(Student::class, 'class_enrollments', 'class_id', 'student_id')
        ->withPivot('enrollment_date', 'is_active')
        ->withTimestamps();
    }

    public function sessionns(): HasMany
    {
        return $this->hasMany(Sessionn::class);
    }

    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id'); // Link to User model
    }

}