import { But<PERSON> } from './ui/button';
import { Badge, Calendar, Image } from 'lucide-react';

interface ProfileHeaderProps {
  coverPhoto: string;
  profilePicture: string;
  name: string;
  playerNumber: number;
  position: string;
  team: string;
  joinDate: string;
}

const ProfileHeader = ({
  coverPhoto,
  profilePicture,
  name,
  playerNumber,
  position,
  team,
  joinDate,
}: ProfileHeaderProps) => {
  return (
    <div className="relative w-full animate-fade-in">
      {/* Cover Photo */}
      <div className="relative w-full h-64 md:h-96 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-black/10 z-10"></div>
        <img
          src={coverPhoto}
          alt="Cover"
          className="w-full h-full object-cover"
        />
        <Button 
          variant="secondary" 
          size="sm" 
          className="absolute top-4 right-4 z-20 flex items-center gap-2 bg-white/80 hover:bg-white"
        >
          <Image size={16} />
          <span className="hidden sm:inline">Change Cover</span>
        </Button>
      </div>

      {/* Profile Picture and Basic Info */}
      <div className="relative px-4 sm:px-6 lg:px-8 pb-6 z-20">
        <div className="flex flex-col sm:flex-row gap-4 sm:items-end -mt-16 sm:-mt-24">
          {/* Profile Picture */}
          <div className="relative">
            <div className="w-32 h-32 sm:w-48 sm:h-48 rounded-full border-4 border-background overflow-hidden bg-white flex-shrink-0 shadow-lg">
              <img
                src={profilePicture}
                alt={name}
                className="w-full h-full object-cover"
              />
            </div>
            <Button
              variant="secondary"
              size="icon"
              className="absolute bottom-0 right-0 rounded-full bg-white hover:bg-white shadow-md"
            >
              <Image size={16} />
            </Button>
          </div>

          {/* Basic Info */}
          <div className="flex flex-col sm:flex-row justify-between w-full gap-4">
            <div>
              <h1 className="text-2xl sm:text-4xl font-bold flex items-center gap-2 text-bball-dark-purple">
                {name} 
                <span className="text-lg sm:text-2xl font-semibold text-bball-gold">#{playerNumber}</span>
              </h1>
              <div className="flex items-center gap-2 mt-1 text-muted-foreground">
                <span className="font-medium">{position}</span>
                <span>•</span>
                <span className="font-medium">{team}</span>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <Calendar size={14} />
                  <span>Joined {joinDate}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2 mt-2 sm:mt-0">
              <Button variant="outline" size="sm" className="px-3 flex items-center gap-2 border-bball-gold text-bball-gold hover:bg-bball-gold/10">
                <Badge size={16} />
                <span>8 Badges</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
