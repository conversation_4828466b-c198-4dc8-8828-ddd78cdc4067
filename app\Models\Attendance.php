<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Attendance extends Model
 {
   use HasFactory;

  protected $fillable = ['sessionn_id', 'student_id', 'attended', 'notes'];

 protected $casts = [
    'attended' => 'boolean',
  ];

   public function sessionn(): BelongsTo
 {
   return $this->belongsTo(Sessionn::class);
  }

   public function student(): BelongsTo
   {
    return $this->belongsTo(Student::class);
    }

   }