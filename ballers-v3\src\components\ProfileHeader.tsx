interface ProfileHeaderProps {
  coverPhoto: string;
  profilePicture: string;
  name: string;
  playerNumber: number;
  position: string;
  team: string;
  joinDate: string;
}

const ProfileHeader = ({
  coverPhoto,
  profilePicture,
  name,
  playerNumber,
  position,
  team,
  joinDate,
}: ProfileHeaderProps) => {
  return (
    <div className="relative">
      <div className="h-48 md:h-64 w-full">
        <img
          src={coverPhoto}
          alt="Cover"
          className="w-full h-full object-cover"
        />
      </div>
      <div className="container max-w-7xl">
        <div className="relative -mt-16 md:-mt-20 px-4">
          <div className="flex flex-col md:flex-row items-center md:items-end gap-4">
            <div className="relative">
              <img
                src={profilePicture}
                alt={name}
                className="w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-white object-cover"
              />
            </div>
            <div className="text-center md:text-left mb-4">
              <h1 className="text-2xl md:text-3xl font-bold">{name}</h1>
              <p className="text-muted-foreground">#{playerNumber} • {position}</p>
              <p className="text-sm text-muted-foreground mt-1">
                {team} • Joined {joinDate}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader; 