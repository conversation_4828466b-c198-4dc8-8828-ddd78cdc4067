/* FixedButton.css */
.fixed-button {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 999;
  }
  
  .fixed-button button {
    background-color: #DC552C;
    font-weight: 400;
    color: #fff;
    padding: 8px 15px;
    border: none;
    cursor: pointer;
    position: relative;
    -webkit-border-radius: 3rem;
    -moz-border-radius: 3rem;
    -ms-border-radius: 3rem;
    -o-border-radius: 3rem;
    border-radius: 3rem;
}
  
  .fixed-button button:hover {
    background-color: #404969;
  }
  
  .close-button {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #BDE4F4;
    color: #000;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
    margin: -5px;
    font-size: 0.6rem;
  }
  
  .close-button:hover {
    background-color: #814040;
  }
  
  button {
    font-size: 1rem;
  }




