<?php

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\StudentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use EightyNine\ExcelImport\ExcelImportAction;
use Filament\Forms\Components\TextInput;
use Carbon\Carbon;
use Carbon\Exceptions\InvalidFormatException;

class ListStudents extends ListRecords
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ExcelImportAction::make()
                ->slideOver()
                ->color('primary')
                ->validateUsing([
                    'arabic_name' => ['nullable', 'string'],
                    'english_name' => ['nullable', 'string'],
                    'email' => ['nullable', 'string'],
                    'parent_number' => ['nullable', 'numeric'],
                    'dob' => ['nullable', 'date'],
                    'gender' => ['nullable', 'string'],
                    'age' => ['nullable'],
                    'level_of_player' => ['nullable', 'string'],
                    'position' => ['nullable', 'string'],
                    'category' => ['nullable', 'string'],
                    'weight' => ['nullable'],
                    'height' => ['nullable'],
                    'school_name' => ['nullable', 'string'],
                    'instagram' => ['nullable', 'string'],
                    'facebook' => ['nullable', 'string'],
                ])
                ->sampleExcel(
                    sampleData: [
                        [
                            'arabic_name' => 'جون دو',
                            'english_name' => 'John Doe',
                            'email' => '<EMAIL>',
                            'parent_number' => '1234567890',
                            'dob' => '2010-01-01',
                            'gender' => 'male',
                            'age' => '15',
                            'level_of_player' => 'Intermediate',
                            'position' => 'Forward',
                            'category' => 'Junior',
                            'weight' => '70',
                            'height' => '175',
                            'school_name' => 'International School',
                        
                        ],
                    ],
                    fileName: 'students-sample.xlsx'
                )
                ->mutateBeforeValidationUsing(function(array $row) {
                    // Check if row is completely empty
                    $hasData = false;
                    foreach ($row as $value) {
                        if (!empty($value) && $value !== '') {
                            $hasData = true;
                            break;
                        }
                    }

                    // If row is empty, return empty array instead of false
                    if (!$hasData) {
                        return [];
                    }

                    // Process date if exists
                    if (!empty($row['dob'])) {
                        try {
                            if (str_contains($row['dob'], '/')) {
                                $dateParts = explode('/', trim($row['dob']));
                                if (count($dateParts) === 3) {
                                    $day = (int)$dateParts[0];
                                    $month = (int)$dateParts[1];
                                    $year = (int)$dateParts[2];

                                    if ($year < 100) {
                                        $year += 2000;
                                    }

                                    if (checkdate($month, $day, $year)) {
                                        $row['dob'] = sprintf('%04d-%02d-%02d', $year, $month, $day);
                                    } else {
                                        $row['dob'] = null;
                                    }
                                }
                            } else {
                                $date = Carbon::parse($row['dob']);
                                $row['dob'] = $date->format('Y-m-d');
                            }
                        } catch (\Exception $e) {
                            \Log::warning("Date parsing failed for: " . $row['dob']);
                            $row['dob'] = null;
                        }
                    }

                    return $row;
                })
                ->mutateAfterValidationUsing(function(array $row) {
                    // Set default values
                    $row['active'] = true;
                    $row['registration_date'] = now()->format('Y-m-d');
                    $row['balance'] = 0.00;
                    return $row;
                })
                ->afterImport(function (array $data, $livewire) {
                    \Filament\Notifications\Notification::make()
                        ->success()
                        ->title('Import Completed')
                        ->body('Students have been imported successfully.')
                        ->send();
                }),
            Actions\CreateAction::make(),
        ];
    }
}

