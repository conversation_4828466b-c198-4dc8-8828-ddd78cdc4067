.Mangement {
    padding-left: 20px;
    padding-right: 20px;
    text-align: center;
    margin-top: 72px;
    max-width: 100%;
  }
  .Mangement h2 {
    font-size: 40px;
    font-weight: 600;
    line-height: 48px;
    letter-spacing: 0px;
    margin-top: 100px;
    /* color: #130f3e; */
    color: #DC552C;
  }
  .Mangement p {
    font-size: 24px;
    font-weight: 400;
    line-height: 29px;
    letter-spacing: 0px;
    margin-top: 8px;
    /* color: #130f3e; */
    color: #404969;
  }
  .container-mang {
    /* display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 2fr));
    align-items: center;
    justify-content: center;
    gap: 40px 92px;
    max-width: 100%;
    width: calc(100% - 150px);
    margin: 50px auto 0 auto; */
    display: flex;
    align-items: center;
    justify-content: center;
    justify-content: space-evenly;
    flex-wrap: wrap;
  }
  
  .container-mang .member {
    /* display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; */

    display: flex;
    flex-direction: column;
    align-items: center; /* Center horizontally */
    justify-content: center; /* Center vertically */
    text-align: center; /* Center text */
    margin-top: 20px; /* Adjust margin as needed */
  }
  
  
  @media (max-width: 767px) {
    .Mangement {
      padding-left: 0px;
    }
    .container-mang {
      width: calc(100% - 10px);
    }
  }
  @media (max-width: 605px) {
    .Mangement h2 {
      font-size: 30px;
      margin-top: 100px;
    }
    .Mangement p {
      font-size: 14px;
      margin-top: 8px;
    }
  }
  
  .member h4 {
    font-size: 20px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0px;
    /* color: #130f3e; */
    color : #404969;
    margin-top: 16px;
  }
  .member h6 {
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: 0px;
    /* color: #130f3ed9; */
    color : #404969;
    margin-top: 8px;
  }
  
  .icons-person a {
  font-size: 22px;
  }

  /* Container for the slider */
.slider-container {
    display: flex;
    align-items: center;
    overflow: hidden;
    width: 100%;
    position: relative;
}
/* Container for the slider */
.slider-container {
    display: flex;
    align-items: center;
    overflow: hidden;
    width: 100%;
    position: relative;
}

/* Image wrapper */
.slider {
    display: flex;
    -webkit-animation: slide 40s linear infinite;
    animation: slide 40s linear infinite;
}

/* Individual image */
.slider img {
    width: 150px; /* Default small size */
    height: 150px;
    border-radius: 50%;
    border: 3px solid #BDE4F4;
    margin: 0 10px;
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 0.7; /* Make non-focused images semi-transparent */
}

/* Focused image (large) */
.slider img.active {
    width: 200px; /* Larger size */
    height: 200px;
    opacity: 1; /* Fully visible */
}

/* Keyframes for sliding animation */
@keyframes slide {
    0% {
        transform: translateX(0); /* Start from the right */
    }
    50% {
        transform: translateX(-50%);
}
    100% {
        transform: translateX(-100%); /* Slide to the left */
    }
}
.parallax {
  overflow: hidden; /* Hide scrollbar */
  position: relative;
  width: 100%;
}

.parallax .scroller {
  display: flex;
  white-space: nowrap;
  flex-wrap: nowrap;
  width: max-content; /* Expand to fit all children */
}

.parallax .member {
  display: inline-block;
  margin-right: 30px; /* Adjust spacing between members */
  text-align: center;
  flex: 0 0 auto; /* Prevent flex items from shrinking */
}

.parallax .image-member {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 3px solid #BDE4F4;
  margin-bottom: 10px;
}