
.story {
    text-align: center;
    padding-top: 72px;
    /* padding-bottom: 30px; */
    margin-bottom: 3rem;
    font-family: "<PERSON>sis", sans-serif;
  }
  
  .story-heading {
    margin-bottom: 40px;
    color: #DC552C;
    font-size: 40px;
    font-weight: 600;
    line-height: 48px;
    letter-spacing: 0em;
    text-align: center;
  }
  
  .story-container-left img {
    max-width: 100%;
    height: auto;
    width: 505px;
    border-radius: 15px;
  }
  
  .story-container-right {
    text-align: left;
    color: #777777;

  }
  .story-container-right .person,
  .story-container-right .role {
    text-align: center;
    color: #404969;
    opacity: 0.8;

  }
  
  .right-text {
    text-align: justify;
    font-family: Lato;
    font-size: 20px;
    font-weight: 400;
    line-height: 34px;
    letter-spacing: 0px;
    color: #777777;
}
.right-text  .icons-person a{
  text-align: justify;
  font-family: Lato;
  font-size: 20px;
  font-weight: 400;
  line-height: 34px;
  letter-spacing: 0px;
  color: #777777;
}
.icons-person a {
  font-size: 22px;
  }
  
  .person {
    text-align: start;
  
    font-family: Lato;
    font-size: 20px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0em;
    color: #130f3e;
  }
  
  .role {
    text-align: start;
    font-family: Lato;
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0em;
    color: #a7a4be;
  }

  .story-container {
    margin-bottom: 100px;
  }
  .story-container p {
    text-align: left;
    /* padding: 10px 100px; */
    color: #130f3e;
  }
  
  .right-text-para {
    margin-top: -10px;
    /* padding: 5px; */
  
    font-family: "Dosis", sans-serif;
    font-size: 18px;
    font-weight: 400;
    line-height: 34px;
    letter-spacing: 0px;
  }
  
  .right-text-para > p {
    margin-bottom: 46px;
    /* font-size: 19px; */
  }
  
  @media only screen and (max-width: 768px) {
    .story img {
      width: 100%;
    }
    /* .story-container p {
      text-align: left;
      padding: 10px 10px;
      color: #130f3e;
    } */
    .story-container-right {
      padding-inline: 50px;
    }
  }
  
  @media only screen and (max-width: 480px) {
    .story-heading {
      font-size: 28px;
      line-height: 30px;
    }
  
    .story-container p {
      /* padding: 10px 20px; */
    }
  }
  
  @media only screen and (max-width: 1286px) {
    .right-text-para {
      /* padding-inline: 90px; */
      font-size: 16px;
    }
  }
  
  /* New Story Top */
  .story-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
    grid-gap: 0;
    margin-top: 20px;
    width: calc(100% - 70px);
    margin: 0 auto -32px auto;
  }
  
  /* .story-container-left img {
    width: 100%;
    max-width: 100%;
    height: auto;
  } */
  
  .story-container-right {
    text-align: left;
    margin-left: -30px;
  }
  
  .right-text {
    padding: 20px;
  }
  
  .person {
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .role {
    text-align: center;
    margin-bottom: 30px;
  }
  .role a {
    text-align: center;
    margin-bottom: 30px;
    font-size: 22px;
  }
  
  @media only screen and (max-width: 768px) {
    .story-container {
      grid-template-columns: 1fr;
      width: calc(100% - 20px);
    }
  
    .story-container-left,
    .story-container-right {
      text-align: center;
    }
  }
  
  /* .right-text-para p {
    font-size: 20px !important;
  } */
  