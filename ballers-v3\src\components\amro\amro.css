@import url("https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.ttt {
  min-height: 100vh;
  min-height: 100svh;
  background: #1f2c3a;
  font-family: "Nunito", sans-serif;
  position: relative;
  overflow-x: hidden;
}

.amro-container {
  position: relative;
}

.circle-top {
  width: clamp(25rem, 30vw, 31.25rem);
  position: absolute;
  top: 3em;
  left: -8em;
  height: clamp(25rem, 30vw, 31.25rem);
  background: hsla(211, 100%, 28%, 1);
  background: radial-gradient(
    circle,
    #00458f8f 0%,
    #00458e00 60%,
    #ffffff00 100%
  );
  background: -moz-radial-gradient(
    circle,
    #00458f8f 0%,
    #00458e00 60%,
    #ffffff00 100%
  );
  background: -webkit-radial-radial-gradient(
    circle,
    #00458f8f 0%,
    #00458e00 60%,
    #ffffff00 100%
  );
  border-radius: 50%;
}

.circle-bottom {
  width: clamp(25rem, 30vw, 31.25rem);
  position: absolute;
  bottom: -5em;
  right: -8em;
  height: clamp(25rem, 30vw, 31.25rem);
  background: hsla(211, 100%, 28%, 1);
  background: radial-gradient(
    circle,
    #00458f8f 0%,
    #00458e00 65%,
    #ffffff00 100%
  );
  background: -moz-radial-gradient(
    circle,
    #00458f8f 0%,
    #00458e00 65%,
    #ffffff00 100%
  );
  background: -webkit-radial-radial-gradient(
    circle,
    #00458f8f 0%,
    #00458e00 65%,
    #ffffff00 100%
  );
  border-radius: 50%;
}