import React from 'react';
import './ImageContainer.css';

const ImageContainer = ({
  src,
  alt = '',
  className = '',
  aspectRatio = '16/9',
  objectFit = 'cover',
  loading = 'lazy',
  onLoad,
  children,
  ...props
}) => {
  const style = {
    '--aspect-ratio': aspectRatio,
    '--object-fit': objectFit
  };

  return (
    <div className={`image-container ${className}`} style={style} {...props}>
      <img 
        src={src} 
        alt={alt} 
        loading={loading} 
        onLoad={onLoad} 
        className="image-container__img"
      />
      {children && (
        <div className="image-container__overlay">
          {children}
        </div>
      )}
    </div>
  );
};

export default ImageContainer;