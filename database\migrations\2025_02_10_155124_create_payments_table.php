<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
     Schema::create('payments', function (Blueprint $table) {
         $table->id();
         $table->foreignId('student_id')->constrained()->onDelete('cascade');
         $table->date('payment_date');
         $table->decimal('amount', 10, 2);
         $table->string('payment_method');
         $table->text('notes')->nullable();
         $table->foreignId('sessionn_id')->nullable()->constrained()->onDelete('set null'); // Optional
          $table->foreignId('class_id')->nullable()->constrained('class_models')->onDelete('set null');
         $table->timestamps();
     });
    }

    public function down(): void
    {
      Schema::dropIfExists('payments');
    }
};