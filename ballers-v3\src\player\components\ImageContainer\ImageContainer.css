.image-container {
  position: relative;
  width: 100%;
  aspect-ratio: var(--aspect-ratio, 16/9);
  overflow: hidden;
  border-radius: var(--border-radius-md);
}

.image-container__img {
  width: 100%;
  height: 100%;
  object-fit: var(--object-fit, cover);
  transition: transform var(--transition-normal) var(--transition-timing);
  display: block;
}

.image-container:hover .image-container__img {
  transform: scale(1.05);
}

.image-container__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: var(--space-4);
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  color: white;
  opacity: 0;
  transition: opacity var(--transition-normal) var(--transition-timing);
}

.image-container:hover .image-container__overlay {
  opacity: 1;
}