<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        try {
            $request->validate([
                'email' => ['required', 'email'],
                'password' => ['required', 'string', 'min:6'],
            ]);

            $student = Student::where('email', $request->email)
                ->where('active', true)
                ->first();

            // Enhanced debugging
            Log::info('Login attempt details', [
                'email' => $request->email,
                'student_found' => $student ? 'yes' : 'no',
                'student_active' => $student?->active ? 'yes' : 'no',
                'password_provided' => $request->password,
                'stored_hash' => $student ? $student->password : 'no_student',
                'hash_check_result' => $student ? (Hash::check($request->password, $student->password) ? 'true' : 'false') : 'no_student'
            ]);

            if (!$student) {
                throw ValidationException::withMessages([
                    'email' => ['Student not found or inactive.'],
                ]);
            }

            // Direct string comparison for debugging (remove in production)
            Log::info('Password comparison', [
                'raw_password' => $request->password,
                'hashed_password' => $student->password,
            ]);

            if (!Hash::check($request->password, $student->password)) {
                // Return more detailed error for debugging
                return response()->json([
                    'status' => 'error',
                    'message' => 'Password verification failed',
                    'debug' => [
                        'provided_length' => strlen($request->password),
                        'hash_length' => strlen($student->password),
                        'hash_algorithm' => password_get_info($student->password)['algoName']
                    ]
                ], 422);
            }

            // Revoke previous tokens
            $student->tokens()->delete();

            // Generate new token
            $token = $student->createToken('student-app')->plainTextToken;

            return response()->json([
                'status' => 'success',
                'data' => [
                    'token' => $token,
                    'token_type' => 'Bearer',
                    'student' => [
                        'id' => $student->id,
                        'email' => $student->email,
                        'english_name' => $student->english_name,
                        'arabic_name' => $student->arabic_name,
                        'profile_photo' => $student->profile_photo,
                        'gender' => $student->gender,
                        'age' => $student->age,
                        'level_of_player' => $student->level_of_player,
                        'position' => $student->position,
                        'category' => $student->category,
                    ]
                ]
            ], 200);

        } catch (ValidationException $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Login error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred during login',
                'debug' => true
            ], 500);
        }
    }

    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Successfully logged out'
        ]);
    }
}
