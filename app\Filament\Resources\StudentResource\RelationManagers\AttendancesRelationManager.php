<?php

namespace App\Filament\Resources\StudentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class AttendancesRelationManager extends RelationManager
{
    protected static string $relationship = 'attendances';
    protected static ?string $title = 'Attendance History';

    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitle('Attendance')
            ->columns([
                Tables\Columns\TextColumn::make('sessionn.class.class_name')
                    ->label('Class')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sessionn.sessionn_date')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sessionn.start_time')
                    ->label('Time')
                    ->time(),
                Tables\Columns\IconColumn::make('attended')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('attended')
                    ->options([
                        '1' => 'Present',
                        '0' => 'Absent',
                    ]),
                Tables\Filters\Filter::make('date')
                    ->form([
                        Forms\Components\DatePicker::make('from'),
                        Forms\Components\DatePicker::make('until'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['from'],
                                fn($query) => $query->whereHas('sessionn', fn($query) => 
                                    $query->whereDate('sessionn_date', '>=', $data['from'])
                                )
                            )
                            ->when(
                                $data['until'],
                                fn($query) => $query->whereHas('sessionn', fn($query) => 
                                    $query->whereDate('sessionn_date', '<=', $data['until'])
                                )
                            );
                    })
            ])
            ->defaultSort('sessionn.sessionn_date', 'desc')
            ->bulkActions([]);
    }
}
