<?php

namespace App\Filament\Resources\StudentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Services\EnrollmentService;
use App\Models\ClassModel;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class ClassesRelationManager extends RelationManager
{
    protected static string $relationship = 'classes';
    protected static ?string $recordTitleAttribute = 'class_name';
    protected static ?string $title = 'Enrolled Classes';
    protected static ?string $modelLabel = 'Class';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('class_id')
                    ->relationship('class', 'class_name')
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\DatePicker::make('enrollment_date')
                    ->required()
                    ->default(now()),
                Forms\Components\Toggle::make('is_active')
                    ->label('Active')
                    ->default(true),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('class_name')
            ->columns([
                Tables\Columns\TextColumn::make('class_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('pivot.enrollment_date')
                    ->date()
                    ->sortable()
                    ->label('Enrolled On'),
                Tables\Columns\IconColumn::make('pivot.is_active')
                    ->boolean()
                    ->label('Active'),
                Tables\Columns\TextColumn::make('instructor.name')
                    ->label('Instructor'),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('pivot.is_active')
                    ->label('Active Status'),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->preloadRecordSelect()
                    ->after(function (array $data) {
                        $class = ClassModel::find($data['recordId']);
                        
                        DB::transaction(function () use ($class) {
                            DB::table('students')
                                ->where('id', $this->ownerRecord->id)
                                ->update([
                                    'balance' => DB::raw("balance - {$class->price_per_month}")
                                ]);

                            $this->ownerRecord->payments()->create([
                                'class_id' => $class->id,
                                'amount' => $class->price_per_month,
                                'payment_method' => 'pending',
                                'payment_date' => now(),
                                'notes' => 'Enrollment fee'
                            ]);

                            $this->ownerRecord->refresh();

                            Notification::make()
                                ->title('Student Enrolled')
                                ->body("Balance updated: JD {$this->ownerRecord->balance}")
                                ->success()
                                ->send();
                        });
                    }),
            ]);
    }
}
