.headerrrr {
  background: linear-gradient(to top, #1f2c3a 0%, #2f3c4742 100%),
    url("../images/g25.jpeg")
      no-repeat 50% 50% / cover;
  width: 100%;
  min-height: 25em;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #fff;
}

.header-contentt {
  width: min(37.5em, 90%);
  margin-top: 5em;
  position: relative;
  z-index: 10;
}

.header-content span {
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.063rem;
  font-size: clamp(0.8rem, 0.7625rem + 0.1875vw, 0.95rem);
}

.header-content h1 {
  font-size: clamp(1.5rem, 1.375rem + 0.625vw, 2rem);
  font-weight: 700;
}