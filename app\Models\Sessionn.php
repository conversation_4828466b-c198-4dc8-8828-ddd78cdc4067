<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Sessionn extends Model
{
  use HasFactory;

  protected $fillable = ['class_id', 'sessionn_date', 'start_time', 'end_time'];

  protected $casts = [
    'sessionn_date' => 'date',
    'start_time' => 'datetime:H:i',
      'end_time' => 'datetime:H:i',
    ];

    protected static function booted()
    {
        static::created(function ($session) {
            $enrolledStudents = ClassEnrollment::where('class_id', $session->class_id)
                ->active()
                ->get();

            foreach ($enrolledStudents as $enrollment) {
                $session->attendances()->create([
                    'student_id' => $enrollment->student_id,
                    'attended' => false,
                ]);
            }
        });
    }

    public function class(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

 }