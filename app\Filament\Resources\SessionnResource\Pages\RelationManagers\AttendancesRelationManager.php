<?php

namespace App\Filament\Resources\SessionnResource\RelationManagers;

use App\Models\Attendance;
use App\Models\Student;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class AttendancesRelationManager extends RelationManager
{
    protected static string $relationship = 'attendances';
    protected static ?string $title = 'Attendance';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('student_id')
                    ->label('Student')
                    ->options(fn() => Student::all()->pluck('english_name', 'id'))
                    ->required()
                    ->disabled(), // Keep disabled
                Forms\Components\Toggle::make('attended')
                    ->label('Attended')
                    ->inline(false)
                    ->required(),
                Forms\Components\Textarea::make('notes'),
            ]);
    }

 public function table(Table $table): Table
  {
        return $table
            ->recordTitleAttribute('student.english_name')
            ->columns([
               Tables\Columns\TextColumn::make('student.english_name'),
                Tables\Columns\IconColumn::make('attended')->boolean(),
               Tables\Columns\TextColumn::make('notes'),
            ])
           ->filters([]) // No filters needed
          ->headerActions([])  // NO CreateAction
         ->actions([
              Tables\Actions\EditAction::make(), // Keep Edit for attendance/notes
               Tables\Actions\DeleteAction::make(), // Keep Delete
          ])
          ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                 Tables\Actions\DeleteBulkAction::make(),
                 ]),
               ]);
    }
   // This is where the magic happens!
   public function afterMount(): void
 {
   //  dd('afterMount called for session', $this->ownerRecord->id); // Debugging line

     DB::transaction(function () {
            $sessionn = $this->ownerRecord;

           // Get all students enrolled in the class
         $enrolledStudents = $sessionn->class->students; // Use the relationship

          foreach ($enrolledStudents as $student) {
               // Check if an attendance record already exists
             $existingAttendance = Attendance::where('sessionn_id', $sessionn->id)
                 ->where('student_id', $student->id)
                 ->first();

              if (!$existingAttendance) {
                  // Create a new attendance record, defaulting to 'absent'
                 Attendance::create([
                      'sessionn_id' => $sessionn->id,
                     'student_id' => $student->id,
                      'attended' => false,
                      'notes' => '',
                   ]);
            }
          }
      });
   }
}