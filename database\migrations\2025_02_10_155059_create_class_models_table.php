<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('class_models', function (Blueprint $table) { 
            $table->id();
            $table->string('class_name');
            $table->text('description');
            $table->integer('day_of_week'); 
            $table->time('start_time');
            $table->time('end_time');
            $table->foreignId('instructor_id')->nullable()->constrained('users')->onDelete('set null'); 
            $table->integer('capacity');
            $table->decimal('price_per_month', 8, 2);
            $table->softDeletes();
            $table->timestamps();
         });
    }

    public function down(): void
    {
        Schema::dropIfExists('class_models');
    }
};