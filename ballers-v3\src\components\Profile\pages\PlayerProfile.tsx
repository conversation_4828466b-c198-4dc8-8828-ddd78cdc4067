import ProfileHeader from '../components/ProfileHeader';
import InfoCard from '../components/InfoCard';
import AttendanceStats from '../components/AttendanceStats';
import BadgeItem from '../components/BadgeItem';
import PaymentItem from '../components/PaymentItem';
import MediaGallery from '../components/MediaGallery';
import AchievementItem from '../components/AchievementItem';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Button } from '../components/ui/button';
import { Calendar, Image, MessageSquare } from 'lucide-react';

const PlayerProfile = () => {
  // Mock data - in a real app this would come from an API
  const playerData = {
    name: "<PERSON>",
    playerNumber: 23,
    position: "Shooting Guard",
    team: "Ballers Academy",
    joinDate: "Jan 15, 2024",
    coverPhoto: "https://images.unsplash.com/photo-1518495973542-4542c06a5843",
    profilePicture: "https://images.unsplash.com/photo-1581092795360-fd1ca04f0952",
    age: 16,
    height: "6'2\"",
    weight: "175 lbs",
    currentCourse: "Advanced Shooting Drills",
    coach: "Amro Al-Wageei",
    attendance: {
      attended: 18,
      missed: 2,
      rate: 90
    },
    badges: [
      {
        name: "Sharpshooter",
        description: "Exceptional accuracy from the three-point line. Awarded to players who consistently hit over 80% in drills.",
        earnedDate: "Apr 10, 2024",
      },
      {
        name: "Team Captain",
        description: "Recognized for outstanding leadership qualities and ability to guide teammates.",
        earnedDate: "Mar 15, 2024",
      },
      {
        name: "Defensive Specialist",
        description: "Exceptional defensive skills demonstrated consistently in games and practice.",
        earnedDate: "Feb 23, 2024",
      }
    ],
    payments: [
      {
        date: "Apr 1, 2024",
        amount: 150.00,
        description: "Monthly Academy Fee",
        status: "paid" as const,
      },
      {
        date: "Mar 1, 2024",
        amount: 150.00,
        description: "Monthly Academy Fee",
        status: "paid" as const,
      },
      {
        date: "May 1, 2024",
        amount: 150.00,
        description: "Monthly Academy Fee",
        status: "pending" as const,
      }
    ],
    mediaGallery: [
      {
        id: "1",
        type: "image" as const,
        url: "https://images.unsplash.com/photo-1518495973542-4542c06a5843",
        thumbnail: "https://images.unsplash.com/photo-1518495973542-4542c06a5843",
        title: "Championship Game Highlight",
        date: "Apr 12, 2024"
      },
      {
        id: "2",
        type: "image" as const,
        url: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158",
        thumbnail: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158",
        title: "Practice Session",
        date: "Mar 28, 2024"
      },
      {
        id: "3",
        type: "image" as const,
        url: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b",
        thumbnail: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b",
        title: "Team Photo",
        date: "Feb 15, 2024"
      },
      {
        id: "4",
        type: "image" as const,
        url: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7",
        thumbnail: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7",
        title: "Skills Workshop",
        date: "Jan 22, 2024"
      }
    ],
    achievements: [
      {
        title: "Regional Championship",
        date: "Apr 2024",
        description: "1st place in the Regional Youth Basketball Championship",
        medal: "gold" as const
      },
      {
        title: "City Tournament",
        date: "Feb 2024",
        description: "2nd place in the Annual City Basketball Tournament",
        medal: "silver" as const
      },
      {
        title: "Holiday Classic",
        date: "Dec 2023",
        description: "Participated in the Holiday Classic Tournament",
        medal: null
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white">
      <ProfileHeader
        coverPhoto={playerData.coverPhoto}
        profilePicture={playerData.profilePicture}
        name={playerData.name}
        playerNumber={playerData.playerNumber}
        position={playerData.position}
        team={playerData.team}
        joinDate={playerData.joinDate}
      />

      <div className="container max-w-7xl pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
          {/* Left Column - Personal Info & Attendance */}
          <div className="space-y-6">
            <InfoCard title="Personal Information" className="border-t-4 border-t-bball-purple">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Age</span>
                  <span className="text-sm font-medium">{playerData.age}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Height</span>
                  <span className="text-sm font-medium">{playerData.height}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Weight</span>
                  <span className="text-sm font-medium">{playerData.weight}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Course</span>
                  <span className="text-sm font-medium">{playerData.currentCourse}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Coach</span>
                  <span className="text-sm font-medium">{playerData.coach}</span>
                </div>
              </div>
            </InfoCard>

            <InfoCard title="Attendance" description="Current season attendance record" className="border-t-4 border-t-bball-gold">
              <AttendanceStats
                attended={playerData.attendance.attended}
                missed={playerData.attendance.missed}
                rate={playerData.attendance.rate}
              />
              <div className="flex justify-end mt-4">
                <Button className="text-xs flex items-center gap-1 border border-bball-purple text-bball-purple hover:bg-bball-purple/10">
                  <Calendar size={14} />
                  <span>View Schedule</span>
                </Button>
              </div>
            </InfoCard>

            <InfoCard title="Financial Information" className="border-t-4 border-t-bball-purple">
              <div className="space-y-3">
                {playerData.payments.map((payment, index) => (
                  <PaymentItem
                    key={index}
                    date={payment.date}
                    amount={payment.amount}
                    description={payment.description}
                    status={payment.status}
                  />
                ))}
                <div className="flex justify-between pt-2">
                  <span className="text-sm font-medium">Total Due:</span>
                  <span className="text-sm font-bold">$150.00</span>
                </div>
                <Button className="w-full bg-bball-gold hover:bg-bball-gold/90 text-black mt-2 font-bold">
                  Make Payment
                </Button>
              </div>
            </InfoCard>
          </div>

          {/* Middle & Right Columns (combined on mobile) */}
          <div className="lg:col-span-2 space-y-6">
            <Tabs defaultValue="achievements" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-bball-light-gray rounded-lg p-1">
                <TabsTrigger value="achievements" className="data-[state=active]:bg-bball-purple data-[state=active]:text-white">Achievements</TabsTrigger>
                <TabsTrigger value="badges" className="data-[state=active]:bg-bball-purple data-[state=active]:text-white">Badges & Skills</TabsTrigger>
                <TabsTrigger value="media" className="data-[state=active]:bg-bball-purple data-[state=active]:text-white">Media Gallery</TabsTrigger>
              </TabsList>
              <TabsContent value="achievements" className="mt-4">
                <InfoCard title="Player Achievements" description="Tournaments and honors" className="border-t-4 border-t-bball-gold">
                  <div className="space-y-3">
                    {playerData.achievements.map((achievement, index) => (
                      <AchievementItem
                        key={index}
                        title={achievement.title}
                        date={achievement.date}
                        description={achievement.description}
                        medal={achievement.medal}
                      />
                    ))}
                  </div>
                </InfoCard>
              </TabsContent>
              <TabsContent value="badges" className="mt-4">
                <InfoCard title="Earned Badges" description="Skills and accomplishments" className="border-t-4 border-t-bball-purple">
                  <div className="space-y-3">
                    {playerData.badges.map((badge, index) => (
                      <BadgeItem
                        key={index}
                        name={badge.name}
                        description={badge.description}
                        earnedDate={badge.earnedDate}
                      />
                    ))}
                  </div>
                </InfoCard>
              </TabsContent>
              <TabsContent value="media" className="mt-4">
                <InfoCard title="Media Gallery" description="Photos and videos from games and training" className="border-t-4 border-t-bball-gold">
                  <MediaGallery items={playerData.mediaGallery} />
                  <div className="flex justify-between mt-4">
                    <Button className="text-xs flex items-center gap-1 border border-bball-purple text-bball-purple hover:bg-bball-purple/10">
                      <MessageSquare size={14} />
                      <span>Request Media</span>
                    </Button>
                    <Button className="text-xs flex items-center gap-1 border border-bball-purple text-bball-purple hover:bg-bball-purple/10">
                      <Image size={14} />
                      <span>View All</span>
                    </Button>
                  </div>
                </InfoCard>
              </TabsContent>
            </Tabs>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InfoCard title="Performance Stats" className="md:col-span-2 border-t-4 border-t-bball-purple">
                <div className="py-8 flex items-center justify-center border-2 border-dashed rounded-md border-muted">
                  <p className="text-muted-foreground text-sm">Advanced statistics coming soon</p>
                </div>
              </InfoCard>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlayerProfile;
