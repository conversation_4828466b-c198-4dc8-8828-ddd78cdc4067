@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

:root {
  /* ### Primary */

  --Red: hsl(0, 100%, 74%);
  --Green: hsl(154, 59%, 51%);

  /* ### Accent */

  --Blue: hsl(248, 32%, 49%);

  /* ### Neutral */

  --Dark-Blue: hsl(249, 10%, 26%);
  --Grayish-Blue: #D16459;
}

.showcase {
  /* background-image: url("./images/bg-intro-mobile.png"); */
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  max-width: 100%;
  /* height:100vh; */
  height: calc(100vh - 0.1px);
}

.overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #252525;
  height: calc(100vh - 0.1px);
  padding: 3rem 1.5rem;
}

.overlay h2 {
  font-size: 2rem;
  margin-bottom: 0.7rem;
  color: #DC552C;

  font-family: Lato;
  font-size: 40px;
  font-weight: 600;
  line-height: 48px;
  letter-spacing: 0px;
  text-align: left;
}
.overlay p {
  margin-bottom: 2rem;
  line-height: 1.8;
  color: #404969;
  font-family: Lato;
  font-size: 24px;
  font-weight: 400;
  line-height: 29px;
  letter-spacing: 0px;
  text-align: left;
}
.tag {
  background-color: var(--Blue);
  padding: 0.3rem 1rem;
  text-align: center;
  /* box-shadow: 0px 7px 0px rgba(0, 0, 0, 0.3); */
  -webkit-border-radius: 0.3rem;
  -moz-border-radius: 0.3rem;
  -ms-border-radius: 0.3rem;
  -o-border-radius: 0.3rem;
  border-radius: 0.3rem;
}
.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #151515;
  padding: 2rem;
  border-radius: 0.5rem;
  -webkit-border-radius: 0.5rem;
  -moz-border-radius: 0.5rem;
  -ms-border-radius: 0.5rem;
  -o-border-radius: 0.5rem;
  /* box-shadow: 0px 7px 0px rgba(0, 0, 0, 0.3); */
}

.form input {
  padding: 0.8rem 1rem;
  /* border-radius: 0.5rem;
  -webkit-border-radius: 0.5rem;
  -moz-border-radius: 0.5rem;
  -ms-border-radius: 0.5rem;
  -o-border-radius: 0.5rem; */
  border: none;
  border-bottom: 1px solid var(--Grayish-Blue);
  /* font-weight: bold; */
  letter-spacing: 0.05rem;
  font-size: 1rem;
  color: #f1c6c2;
  margin-bottom: 20px;
  background-color: #151515;
}
.form input:active{
  color: #D16459;
}
.form input:active{
  border-style: dotted solid #D16459;
}

.form button {
  background-color: #D16459;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
  /* text-transform: uppercase; */
  font-weight: 400;
  height: 40px;
  width: 472px;
  border-radius: 23px;
  padding: 0.5rem 2rem;
  border-radius: 0.5rem;
  -webkit-border-radius: 23px;
  -moz-border-radius: 23px;
  -ms-border-radius: 23px;
  -o-border-radius: 23px;
  /*box-shadow: 0px 7px 0px hsl(154, 58%, 42%);
    */
}
.form small {
  color: var(--Dark-Blue);
  text-align: center;
}
.form small span {
  color: var(--Red);
  font-weight: bold;
}
@media (min-width: 768px) {
  /* article {
    margin-top: 100px;
  } */
  .showcase {
    /* background-image: url("./images/bg-intro-desktop.png"); */
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }
  .overlay {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    height: 100vh;
    max-width: 1200px;
    margin: auto;
    gap: 2rem;
  }
  .overlay h1 {
    font-size: 3rem;
  }
  .overlay p {
    font-size: 1.25rem;
  }
}

.red {
  color: var(--Red);
}

.details-container {
  display: flex;
  flex-direction: column;
  justify-content: left;
}
.details-container .details {
  display: flex;
  font-family: Lato;
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0em;
  text-align: left;
  color: #bbbbbb;
  flex-wrap: wrap;
}

.details-container .details img {
  margin-right: 16px;
}
.details-container .details:first-child img {
  margin-right: 16px;
  margin-top: 10px;
}


.from-image {
  display: flex;
  justify-content: left;
  flex-wrap: wrap;
}
.from-image img {
  margin-right: 16px;
}

@media (max-width: 768px) {
  .showcase {
    margin-top: 100px;
    margin-bottom: 100px;
  }
}

.check {
  display: flex;
  justify-content: left;
  color: #130f3e;
}
.check label {
  margin-left: 16px;
  margin-top: -5px;
  color: #777777;
}
