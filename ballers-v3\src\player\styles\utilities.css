/* Flex utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1 1 0%;
}

/* Grid utilities */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-2 {
  gap: var(--space-2);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.gap-8 {
  gap: var(--space-8);
}

/* Spacing utilities */
.mt-2 {
  margin-top: var(--space-2);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-6 {
  margin-top: var(--space-6);
}

.mt-8 {
  margin-top: var(--space-8);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.ml-2 {
  margin-left: var(--space-2);
}

.mr-2 {
  margin-right: var(--space-2);
}

.p-2 {
  padding: var(--space-2);
}

.p-4 {
  padding: var(--space-4);
}

.p-6 {
  padding: var(--space-6);
}

.p-8 {
  padding: var(--space-8);
}

.py-2 {
  padding-top: var(--space-2);
  padding-bottom: var(--space-2);
}

.py-4 {
  padding-top: var(--space-4);
  padding-bottom: var(--space-4);
}

.py-6 {
  padding-top: var(--space-6);
  padding-bottom: var(--space-6);
}

.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

.px-2 {
  padding-left: var(--space-2);
  padding-right: var(--space-2);
}

.px-4 {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.px-6 {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
}

.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}

/* Text utilities */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.font-normal {
  font-weight: var(--font-weight-normal);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

/* Color utilities */
.text-primary {
  color: var(--color-primary-600);
}

.text-secondary {
  color: var(--color-secondary-600);
}

.text-accent {
  color: var(--color-accent-600);
}

.text-success {
  color: var(--color-success-600);
}

.text-warning {
  color: var(--color-warning-600);
}

.text-error {
  color: var(--color-error-600);
}

.text-neutral {
  color: var(--color-neutral-600);
}

.bg-primary {
  background-color: var(--color-primary-600);
}

.bg-secondary {
  background-color: var(--color-secondary-600);
}

.bg-accent {
  background-color: var(--color-accent-600);
}

.bg-success {
  background-color: var(--color-success-600);
}

.bg-warning {
  background-color: var(--color-warning-600);
}

.bg-error {
  background-color: var(--color-error-600);
}

.bg-neutral {
  background-color: var(--color-neutral-100);
}

.bg-white {
  background-color: white;
}

/* Display utilities */
.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.hidden {
  display: none;
}

/* Position utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.top-0 {
  top: 0;
}

.right-0 {
  right: 0;
}

.bottom-0 {
  bottom: 0;
}

.left-0 {
  left: 0;
}

/* Width and height utilities */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:flex {
    display: flex;
  }
  
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .sm\:hidden {
    display: none;
  }
}

@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .md\:hidden {
    display: none;
  }
}

@media (min-width: 1024px) {
  .lg\:flex {
    display: flex;
  }
  
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .lg\:hidden {
    display: none;
  }
}