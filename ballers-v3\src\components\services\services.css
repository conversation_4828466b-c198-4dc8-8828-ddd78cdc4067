@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700;800;900&display=swap");
.services{
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    /* background: #221b33; */
}
.After-services{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 50px;
}
.container_services {
    position: relative;
    width: 1200px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
  }
  
  .container_services .serviceBox {
    position: relative;
    width: 350px;
    height: 280px;
    background-color: #5e6376;
    /* background-color: #6c7082; */
    background-image: '../images/1 (1).jpeg';
    border-radius: 20px;
    overflow: hidden;
    cursor: pointer;
  }
  
  .container_services .serviceBox .icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--i);
    transition: 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    transition-delay: 0.25s;
  }
  
  .container_services .serviceBox:hover .icon {
    top: 30px;
    left: calc(50% - 40px);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    transition-delay: 0s;
  }
  
  .container_services .serviceBox .icon ion-icon {
    font-size: 5em;
    color: #fff;
    transition: 0.5s;
    transition-delay: 0.25s;
  }
  
  .container_services .serviceBox:hover .icon ion-icon {
    font-size: 2em;
    transition-delay: 0s;
  }
  
  .container_services .serviceBox .content_services {
    position: relative;
    padding: 20px;
    color: #fff;
    text-align: center;
    margin-top: 100px;
    z-index: 1;
    transform: scale(0);
    transition: 0.5s;
    transition-delay: 0s;
  }
  
  .container_services .serviceBox:hover .content_services {
    transform: scale(1);
    transition-delay: 0.25s;
  }
  
  .container_services .serviceBox .content_services h2 {
    margin-top: 10px;
    margin-bottom: 5px;
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  .container_services .serviceBox .content_services p {
    font-weight: 300;
    line-height: 1.5em;
  }
  
  @media screen and (max-width: 700px) {
    .container_services{
        margin-bottom: 2rem;
    }
    .container_services .serviceBox .content_services h2 {
      font-size: 1.25rem;
    }
  
    .container_services .serviceBox .content_services p {
      font-size: 1rem;
      padding-inline: 2rem;
    }
  }
  
  @media screen and (max-width: 400px) {
    .container_services .serviceBox {
      margin-inline: 1rem;
      width: 100%;
      height: auto;
    }
  
    .container_services .serviceBox .content_services p {
      padding-inline: initial;
    }
  }

  #testggg {
    font-size: 20px;
    padding-inline: 20%;
    align-items: center;
    text-align: center;
    color: #a1a1a1;
    line-height: 1.8;

  }