<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Support\Enums\Alignment;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'User Management';
    protected static ?string $modelLabel = 'User';
    protected static ?string $pluralModelLabel = 'Users';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('تفاصيل المستخدم')
                    ->columns(2)
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(1)
                            ->label('الاسم')
                            ->placeholder('اسم المستخدم')
                            ->prefixIcon('heroicon-o-user'), // Icon
                        TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(1)
                            ->label('البريد الإلكتروني')
                            ->placeholder('البريد الإلكتروني')
                            ->prefixIcon('heroicon-o-envelope'), // Icon

                        TextInput::make('password')
                            ->label('كلمة المرور')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->maxLength(255)
                            ->same('passwordConfirmation')
                            ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                            ->dehydrated(fn ($state) => filled($state))
                            ->columnSpan(1)
                            ->placeholder('كلمة المرور')
                            ->prefixIcon('heroicon-o-key')//Icon
                            ->autocomplete('new-password'),//Disable Browser password management

                        TextInput::make('passwordConfirmation')
                            ->label('تأكيد كلمة المرور')
                            ->password()
                            ->required(fn (string $context): bool => $context === 'create')
                            ->maxLength(255)
                            ->columnSpan(1)
                            ->placeholder('تأكيد كلمة المرور')
                            ->prefixIcon('heroicon-o-key')//Icon
                             ->autocomplete('new-password'),//Disable Browser password management
                        Select::make('roles')
                            ->multiple()
                            ->relationship('roles', 'name')
                            ->preload()
                            ->columnSpan(2)
                            ->prefixIcon('heroicon-o-shield-check'),//Icon
                    ]),
            ]);
    }
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->label('الاسم')
                    ->icon('heroicon-o-user'), // Icon
                TextColumn::make('email')
                    ->searchable()
                    ->label('البريد الإلكتروني')
                    ->icon('heroicon-o-envelope'), // Icon
                TextColumn::make('roles.name')
                    ->label('الأدوار')
                    ->badge(),
                 TextColumn::make('created_at')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('تاريخ الإنشاء')
                     ->alignment(Alignment::Center),
                TextColumn::make('updated_at')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('تاريخ التحديث')
                     ->alignment(Alignment::Center),
            ])
            ->filters([

            ])
            ->actions([
                Tables\Actions\EditAction::make()
                ->label('تعديل')
                 ->iconButton(),
                 Tables\Actions\DeleteAction::make()
                 ->label('حذف')
                  ->iconButton(),


            ])
            ->bulkActions([

            ]);
              
    }



    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'success';
    }
}
