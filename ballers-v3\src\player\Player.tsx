// import React from 'react';
// Correct single import statement
import {
  <PERSON><PERSON>,
  Con<PERSON>er,
  Card,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>nt,
  CardFooter
} from './components';

import {
  Image as ImageIcon,
  Trophy,
  Target,
  Medal,
  Star,
  FileText,
  Video,
  Bell,
  TrendingUp,
  Camera,
  CalendarDays,
  // Activity,
  // Flame,
} from 'lucide-react';



// Your component code here...

import Navbar from '../components/NavBar/Navbar';
import Footer from '../components/Footer/Footer';

import './styles/global.css';

function App() {
  const playerInfo = {
    name: "<PERSON>",
    number: "#23",
    position: "Shooting Guard",
    team: "Ballers Academy",
    joinDate: "Joined Jan 15, 2024",
    badges: 8,
    personalInfo: {
      age: 16,
      height: "6'2\"",
      weight: "175 lbs",
      course: "Advanced Shooting Drills"
    }
  };

  const attendance = {
    attended: 18,
    missed: 2,
    total: 20,
    rate: 90
  };

  const payments = [
    { id: 1, date: 'Apr 1, 2024', amount: 150.00, status: 'paid' },
    { id: 2, date: 'Mar 1, 2024', amount: 150.00, status: 'paid' },
    { id: 3, date: 'May 1, 2024', amount: 150.00, status: 'pending' }
  ];

  const achievements = [
    { id: 1, title: "Regional Championship", date: "Apr 2024", icon: Trophy },
    { id: 2, title: "Holiday Classic", date: "Dec 2023", icon: Medal }
  ];

  const announcements = [
    {
      id: 1,
      title: "Tournament Schedule Update",
      date: "Apr 15, 2024",
      content: "The Regional Championship schedule has been updated. Please check your email for details."
    },
    {
      id: 2,
      title: "Training Session Change",
      date: "Apr 10, 2024",
      content: "Saturday's training session will be held at the main court instead of the practice facility."
    }
  ];

  const coachMedia = [
    {
      id: 1,
      type: "image",
      url: "https://images.pexels.com/photos/3755440/pexels-photo-3755440.jpeg",
      title: "Jump Shot Form",
      date: "Apr 12, 2024"
    },
    {
      id: 2,
      type: "image",
      url: "https://images.pexels.com/photos/3755442/pexels-photo-3755442.jpeg",
      title: "Defense Positioning",
      date: "Apr 10, 2024"
    }
  ];

  const resources = [
    {
      id: 1,
      type: "pdf",
      title: "Shooting Mechanics Guide",
      size: "2.4 MB",
      date: "Apr 5, 2024"
    },
    {
      id: 2,
      type: "video",
      title: "Advanced Dribbling Techniques",
      duration: "15:30",
      date: "Apr 3, 2024"
    }
  ];

  const performanceStats = {
    shooting: {
      title: "Shooting Accuracy",
      value: "68%",
      trend: "+5%"
    },
    defense: {
      title: "Defensive Rating",
      value: "85",
      trend: "+3"
    },
    stamina: {
      title: "Stamina Level",
      value: "92%",
      trend: "+8%"
    }
  };

  return (

    <div>
      <Navbar />
      <div className="min-h-screen bg-neutral-50">
        <div className="profile-cover">
          <img
            src="https://images.pexels.com/photos/1752757/pexels-photo-1752757.jpeg"
            alt="Cover"
            className="profile-cover__image"
          />
          <Button className="profile-cover__change-btn">
            <ImageIcon size={18} className="mr-2" />
            Change Cover
          </Button>
        </div>

        <Container>
          <div className="profile-header">
            <div className="profile-header__main">
              <div className="profile-avatar">
                <img
                  src="https://images.pexels.com/photos/2834917/pexels-photo-2834917.jpeg"
                  alt={playerInfo.name}
                  className="profile-avatar__image"
                />
                <Button className="profile-avatar__change-btn">
                  <ImageIcon size={14} />
                </Button>
              </div>
              <div className="profile-info">
                <div className="flex items-center gap-3 flex-wrap">
                  <h1 className="profile-info__name">{playerInfo.name}</h1>
                  <span className="profile-info__number">{playerInfo.number}</span>
                  <div className="profile-badges">
                    <Star size={16} className="text-yellow-500" />
                    <span>{playerInfo.badges} Badges</span>
                  </div>
                </div>
                <div className="profile-info__meta">
                  <Target size={14} className="text-primary-500"
                    // fill="#ed8936"
                    strokeWidth={1.5}
                  />
                  <span>{playerInfo.position}</span>
                  <Trophy size={14} className="text-primary-500" />
                  <span>{playerInfo.team}</span>
                  <CalendarDays size={14} className="text-primary-500" />
                  <span> {playerInfo.joinDate}</span>
                </div>
              </div>
            </div>
          </div>

          {/* #6B4EE6 */}

          <div className="profile-content">
            <Card className="mb-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Bell className="text-primary-600" size={24} />
                  <h2 className="card__title">Announcements</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="announcements">
                  {announcements.map(announcement => (
                    <div key={announcement.id} className="announcement-item">
                      <div className="announcement-item__header">
                        <h3 className="announcement-item__title">{announcement.title}</h3>
                        <span className="announcement-item__date">{announcement.date}</span>
                      </div>
                      <p className="announcement-item__content">{announcement.content}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <h2 className="card__title">Personal Information</h2>
                </CardHeader>
                <CardContent>
                  <div className="personal-info">
                    {Object.entries(playerInfo.personalInfo).map(([key, value]) => (
                      <div key={key} className="personal-info__item">
                        <span className="personal-info__label">{key.charAt(0).toUpperCase() + key.slice(1)}</span>
                        <span className="personal-info__value">{value}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <h2 className="card__title">Attendance</h2>
                  <p className="card__subtitle">Current season attendance record</p>
                </CardHeader>
                <CardContent>
                  <div className="card__progress">
                    <div className="card__progress-bar" style={{ width: `${attendance.rate}%` }}></div>
                  </div>
                  <div className="flex justify-between mt-4">
                    <div className="card__stat">
                      <span className="card__stat-value">{attendance.attended}</span>
                      <span className="card__stat-label">Attended</span>
                    </div>
                    <div className="card__stat">
                      <span className="card__stat-value">{attendance.missed}</span>
                      <span className="card__stat-label">Missed</span>
                    </div>
                    <div className="card__stat">
                      <span className="card__stat-value">{attendance.total}</span>
                      <span className="card__stat-label">Total</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <h2 className="card__title">Achievements</h2>
                </CardHeader>
                <CardContent>
                  <div className="achievements">
                    {achievements.map(achievement => (
                      <div key={achievement.id} className="achievement-item">
                        <achievement.icon size={24} className="achievement-item__icon" />
                        <div>
                          <h3 className="achievement-item__title">{achievement.title}</h3>
                          <span className="achievement-item__date">{achievement.date}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <TrendingUp className="text-primary-600" size={24} />
                  <h2 className="card__title">Performance Stats</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {Object.entries(performanceStats).map(([key, stat]) => (
                    <div key={key} className="performance-stat">
                      <h3 className="performance-stat__title">{stat.title}</h3>
                      <div className="performance-stat__values">
                        <span className="performance-stat__main">{stat.value}</span>
                        <span className="performance-stat__trend performance-stat__trend--up">
                          {stat.trend}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Camera className="text-primary-600" size={24} />
                  <h2 className="card__title">Coach Media</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="media-grid">
                  {coachMedia.map(media => (
                    <div key={media.id} className="media-item">
                      <img src={media.url} alt={media.title} className="media-item__image" />
                      <div className="media-item__overlay">
                        <h3 className="media-item__title">{media.title}</h3>
                        <span className="media-item__date">{media.date}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <FileText className="text-primary-600" size={24} />
                  <h2 className="card__title">Resources</h2>
                </div>
              </CardHeader>
              <CardContent>
                <div className="resources">
                  {resources.map(resource => (
                    <div key={resource.id} className="resource-item">
                      {resource.type === 'pdf' ? (
                        <FileText size={24} className="resource-item__icon" />
                      ) : (
                        <Video size={24} className="resource-item__icon" />
                      )}
                      <div className="resource-item__info">
                        <h3 className="resource-item__title">{resource.title}</h3>
                        <span className="resource-item__meta">
                          {resource.type === 'pdf' ? resource.size : resource.duration}
                          <span className="mx-2">•</span>
                          {resource.date}
                        </span>
                      </div>
                      <Button variant="outline" size="sm">Download</Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <h2 className="card__title">Financial Information</h2>
                <p className="card__subtitle">Monthly academy fees and payments</p>
              </CardHeader>
              <CardContent>
                {payments.map(payment => (
                  <div key={payment.id} className="card__payment">
                    <div>
                      <h4 className="font-medium">Monthly Academy Fee</h4>
                      <p className="text-sm text-neutral-600">{payment.date}</p>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium mr-3">${payment.amount.toFixed(2)}</span>
                      <span className={`card__payment-status card__payment-status--${payment.status}`}>
                        {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                      </span>
                    </div>
                  </div>
                ))}
              </CardContent>
              <CardFooter>
                <Button className="w-full">Make Payment</Button>
              </CardFooter>
            </Card>
          </div>
        </Container>
      </div>
      <Footer />
    </div>

  );
}

export default App;