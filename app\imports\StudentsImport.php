<?php

namespace App\Imports;

use App\Models\Student;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Validators\Failure;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;

class StudentsImport implements 
    ToCollection,  // Change from ToModel to ToCollection
    WithHeadingRow,
    WithValidation,
    SkipsOnError,
    SkipsOnFailure,
    WithBatchInserts
{
    /**
     * @var array
     * Mapping between database fields and possible Excel column names
     */
    protected $columnMappings = [
        'profile_photo' => ['PICTURE', 'Picture'],
        'arabic_name' => ['ARABIC NAME', 'Arabic Name'],
        'english_name' => ['ENGLISH NAME', 'English Name'],
        'gender' => ['GENDER', 'Gender'],
        'nationality' => ['NATIONALITY', 'Nationality'],
        'dob' => ['DOB', 'Dob'],
        'age' => ['AGE', 'Age'],
        'parent_number' => ['PARENT NUMBER', 'Parent Number'],
        'level_of_player' => ['LEVEL OF PLAYER', 'Level Of Player'],
        'position' => ['POSITION', 'Position'],
        'category' => ['CATEGORY', 'Category'],
        'weight' => ['WEIGHT', 'Weight'],
        'height' => ['HEIGHT', 'Height'],
        'school_name' => ['SCHOOL NAME', 'School Name'],
        'instagram' => ['INSTAGRAM', 'Instagram'],
        'facebook' => ['FACEBOOK', 'Facebook']
    ];

    /**
     * @var array
     * Cache for found column names
     */
    protected $actualColumns = [];

    /**
     * Find the actual column name in the Excel file
     */
    protected function findActualColumn($field, $row)
    {
        if (isset($this->actualColumns[$field])) {
            return $this->actualColumns[$field];
        }

        foreach ($this->columnMappings[$field] as $possibleName) {
            if (isset($row[$possibleName])) {
                $this->actualColumns[$field] = $possibleName;
                return $possibleName;
            }
        }

        return null;
    }

    /**
     * Transform date values
     */
    private function transformDate($value)
    {
        if (empty($value)) {
            return null;
        }

        try {
            // Handle Excel numeric dates
            if (is_numeric($value)) {
                return Date::excelToDateTimeObject($value);
            }

            // Handle string dates
            if (is_string($value)) {
                // Try multiple date formats
                $formats = ['Y-m-d', 'd-m-Y', 'm/d/Y', 'd/m/Y', 'Y/m/d'];

                foreach ($formats as $format) {
                    try {
                        $date = Carbon::createFromFormat($format, $value);
                        if ($date !== false) {
                            return $date;
                        }
                    } catch (\Exception $e) {
                        continue;
                    }
                }
            }

            // Fallback to general parse
            return Carbon::parse($value);
        } catch (\Exception $e) {
            Log::warning('Date transformation failed', [
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Process the collection of rows
     */
    public function collection(Collection $rows)
    {
        $importedRows = 0;
        $failedRows = 0;

        // Debug first row and headers
        $firstRow = $rows->first();
        Log::info('First row data:', [
            'raw_data' => $firstRow?->toArray(),
            'available_keys' => $firstRow ? array_keys($firstRow->toArray()) : []
        ]);

        foreach ($rows as $index => $row) {
            try {
                // Skip empty rows
                if (empty(array_filter($row->toArray()))) {
                    continue;
                }

                // Debug each row mapping
                Log::info('Processing row:', [
                    'row_number' => $index + 2,
                    'raw_data' => $row->toArray()
                ]);

                // Map the incoming data
                $mappedData = [];
                $rowArray = $row->toArray();
                $rowKeys = array_map('strtoupper', array_keys($rowArray));
                $upperRow = array_change_key_case($rowArray, CASE_UPPER);
                
                foreach ($this->columnMappings as $field => $possibleNames) {
                    $value = null;
                    foreach ($possibleNames as $name) {
                        $upperName = strtoupper($name);
                        if (in_array($upperName, $rowKeys)) {
                            $value = $upperRow[$upperName];
                            break;
                        }
                    }
                    $mappedData[$field] = $value;
                }

                // Debug the mapping
                Log::info('Column mapping:', [
                    'excel_headers' => array_keys($rowArray),
                    'mapped_fields' => array_filter($mappedData)
                ]);

                // Set default values
                $mappedData['registration_date'] = now();
                $mappedData['active'] = true;
                $mappedData['balance'] = 0.00;

                // Only create if we have at least some data
                if (!empty(array_filter($mappedData))) {
                    Student::create($mappedData);
                    $importedRows++;
                }

            } catch (\Exception $e) {
                $failedRows++;
                Log::error('Row import failure', [
                    'row' => $index + 2,
                    'error' => $e->getMessage(),
                    'values' => $row->toArray()
                ]);
            }
        }

        Log::info('Import completed', [
            'total_rows' => $rows->count(),
            'imported' => $importedRows,
            'failed' => $failedRows
        ]);
    }

    /**
     * Update validation rules to handle Arabic input
     */
    public function rules(): array
    {
        return [
            'arabic_name' => 'nullable|string',
            'english_name' => 'nullable|string',
            'gender' => 'nullable|string',
            'nationality' => 'nullable|string',
            'dob' => 'nullable|date',
            'age' => 'nullable|numeric',
            'parent_number' => 'nullable|string',
            'level_of_player' => 'nullable|string',
            'position' => 'nullable|string', 
            'category' => 'nullable|string',
            'weight' => 'nullable|string',
            'height' => 'nullable|string',
            'school_name' => 'nullable|string',
            'instagram' => 'nullable|string',
            'facebook' => 'nullable|string',
            'registration_date' => 'nullable|date' // Changed from required to nullable
        ];
    }

    /**
     * Custom validation messages
     */
    public function customValidationMessages()
    {
        return [
            'arabic_name.required' => 'Arabic name is required',
            'english_name.required' => 'English name is required',
            'gender.required' => 'Gender is required',
            'gender.in' => 'Gender must be male or female',
            'dob.required' => 'Date of birth is required',
            'dob.date' => 'Invalid date format',
            'email.email' => 'Invalid email format',
            'email.unique' => 'Email already exists',
            'parent_number.required' => 'Parent number is required',
        ];
    }

    /**
     * Handle import errors
     */
    public function onError(\Throwable $e)
    {
        Log::error('Import Error', [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }

    /**
     * Handle row failures
     */
    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            Log::warning('Row import failure', [
                'row' => $failure->row(),
                'attribute' => $failure->attribute(), // Log the attribute
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ]);
        }
    }

    /**
     * Set batch size for bulk inserts
     */
    public function batchSize(): int
    {
        return 100;
    }
}
