
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const Index = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to player profile after login (simulating post-login flow)
    navigate('/profile');
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-bball-purple/20 to-white">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4 text-bball-dark-purple">Redirecting to Player Profile...</h1>
        <div className="animate-pulse text-bball-gold">
          Please wait
        </div>
      </div>
    </div>
  );
};

export default Index;
