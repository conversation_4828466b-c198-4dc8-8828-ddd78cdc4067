<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClassEnrollmentResource\Pages;
use App\Filament\Resources\ClassEnrollmentResource\RelationManagers;
use App\Models\ClassEnrollment;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table; // Import the Table class
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Filament\Notifications\Notification;

class ClassEnrollmentResource extends Resource // Correct class definition
{
    protected static ?string $model = ClassEnrollment::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-plus';
    protected static ?string $navigationGroup = 'Player Management';
    protected static ?string $modelLabel = 'Enrollment';
    protected static ?string $pluralModelLabel = 'Enrollments';
    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('student_id')
                    ->label('Student')
                    ->relationship('student', 'english_name')
                    ->searchable()
                    ->preload()
                    ->required(),

                Select::make('class_id')
                    ->label('Class')
                    ->relationship('class', 'class_name')
                    ->searchable()
                    ->preload()
                    ->required(),

                DatePicker::make('enrollment_date')
                    ->label('Enrollment Date')
                    ->required(),

                Toggle::make('is_active')
                    ->label('Active')
                    ->default(true),

                Forms\Components\Hidden::make('user_id')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table // Correct type hint
    {
        return $table
            ->columns([
                TextColumn::make('student.english_name')
                    ->label('Student')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('class.class_name')
                    ->label('Class')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('enrollment_date')
                    ->label('Enrollment Date')
                    ->date()
                    ->sortable(),

                BooleanColumn::make('is_active')
                    ->label('Active')
                    ->sortable(),

                TextColumn::make('user.name')->label('Created By')->searchable(),
                TextColumn::make('created_at')->label('Created At')->dateTime()->sortable(),
                TextColumn::make('updated_at')->label('Updated At')->dateTime()->sortable(),
            ])
            ->filters([
                SelectFilter::make('student_id')
                    ->relationship('student', 'english_name'),

                SelectFilter::make('class_id')
                    ->relationship('class', 'class_name'),

                Filter::make('is_active')
                    ->label('Active Enrollments')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),

                Filter::make('is_inactive')
                    ->label('Inactive Enrollments')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', false)),
            ])
            ->actions([
                EditAction::make()->icon('heroicon-o-pencil-square'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClassEnrollments::route('/'),
            'create' => Pages\CreateClassEnrollment::route('/create'),
            'edit' => Pages\EditClassEnrollment::route('/{record}/edit'),
        ];
    }

    public function class()
    {
        return $this->belongsTo(ClassModel::class, 'class_id'); // Ensure 'class_id' is correct
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'warning';
    }
}