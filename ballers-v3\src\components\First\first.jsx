import React from 'react';
import { 
  FaPhoneAlt, 
  FaMapMarkerAlt, 
  FaCalendarAlt 
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import './first.css';

function First() {
  const { t } = useTranslation();

  const handleClick = () => {
    window.location.href = '/join'; 
  };

  return (
    <main className='first-main'>
      <div className='first-container'>
        <div className='first-card'>
          <div className='first-icon'>
            <FaPhoneAlt
              color={'#a69b9b'}
              size={70}
            /> 
          </div>
          <div className='first-content'>
            <h2>{t('telephone-first')}</h2>
            <p>{t('telephone-first-content1')}</p>
            <p>{t('telephone-first-content2')}</p>
          </div>
        </div>
        <div className='first-card'>
          <div className='first-icon'>
            <FaMapMarkerAlt
              color={'#a69b9b'}
              size={70}
            /> 
          </div>
          <div className='first-content'>
            <h2>{t('location-first')}</h2>
            <p>{t('location-first-content')}</p>
          </div>
        </div>
        <div className='first-card'>
          <div className='first-icon'>
            <FaCalendarAlt
              color={'#a69b9b'}
              size={70}
            /> 
          </div>
          <div className='first-content'>
            <h2>{t('join-first')}</h2>
            <button className="button-81" onClick={handleClick}>
              {t('join-first-content')}
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}

export default First;