<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClassEnrollment extends Model
{
  use HasFactory;

  protected $fillable = ['student_id', 'class_id', 'enrollment_date', 'is_active'];
  protected $casts = ['enrollment_date' => 'date', 'is_active' => 'boolean'];

  public function student(): BelongsTo
  {
    return $this->belongsTo(Student::class);
  }

  public function class(): BelongsTo
  {
    return $this->belongsTo(ClassModel::class);
  }

  public function scopeActive($query)
  {
    return $query->where('is_active', true);
  }
}