<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StudentResource\Pages;
use App\Filament\Resources\StudentResource\RelationManagers;
use App\Models\Student;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Support\Colors\Color;
use Filament\Tables\Actions\BulkAction;
use App\Imports\StudentsImport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Database\Eloquent\Builder;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;

class StudentResource extends Resource
{
    protected static ?string $model = Student::class;
    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';
    protected static ?string $navigationGroup = 'Player Management';
    protected static ?string $modelLabel = 'Player';
    protected static ?string $pluralModelLabel = 'Players';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationLabel = 'Students';
    protected static ?string $recordTitleAttribute = 'english_name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Login Credentials')
                    ->description('Student login information')
                    ->icon('heroicon-m-key')
                    ->columns(2)
                    ->collapsible()
                    ->schema([
                        TextInput::make('email')
                            ->email()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('Enter email address'),

                        TextInput::make('password')
                            ->password()
                            ->required(fn(string $operation): bool => $operation === 'create')
                            ->minLength(6)
                            // Remove the dehydrate logic since hashing is handled in the model
                            ->dehydrated(fn(?string $state): bool => filled($state))
                            ->label(
                                fn(string $operation): string =>
                                $operation === 'create' ? 'Password' : 'New password'
                            )
                            ->placeholder('Enter password'),
                    ]),

                Section::make('Student Information')
                    ->description('Basic details and contact information')
                    ->icon('heroicon-m-user-circle')
                    ->columns(2)
                    ->collapsible()
                    ->schema([
                        FileUpload::make('profile_photo')
                            ->image()
                            ->imageEditor()
                            ->disk('public')
                            ->directory('student-photos')
                            ->visibility('public')
                            ->columnSpanFull(),

                        TextInput::make('english_name')
                            ->required()
                            ->maxLength(255)
                            ->autofocus()
                            ->placeholder('Enter english name'),

                        TextInput::make('arabic_name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter Arabic name'),

                        TextInput::make('gender')
                            ->required()
                            ->maxLength(50)
                            ->placeholder('Enter gender'),

                        DatePicker::make('dob')
                            ->required()
                            ->displayFormat('d/m/Y')
                            ->placeholder('dd/mm/yyyy')
                            ->closeOnDateSelection(),

                        TextInput::make('age')
                            ->required()
                            ->maxLength(3)
                            ->placeholder('Enter age'),

                        TextInput::make('parent_number')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter parent number'),

                        TextInput::make('level_of_player')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter level of player'),

                        TextInput::make('position')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter position'),

                        TextInput::make('category')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter category'),

                        TextInput::make('weight')
                            ->nullable()
                            ->maxLength(10)
                            ->placeholder('Enter weight'),

                        TextInput::make('height')
                            ->nullable()
                            ->maxLength(10)
                            ->placeholder('Enter height'),

                        TextInput::make('school_name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter school name'),

                        TextInput::make('instagram')
                            ->maxLength(255)
                            ->placeholder('Enter Instagram username'),

                        TextInput::make('facebook')
                            ->maxLength(255)
                            ->placeholder('Enter Facebook username'),

                        Textarea::make('address')
                            ->columnSpanFull()
                            ->placeholder('Enter full address')
                            ->rows(3),
                        Forms\Components\Select::make('coach_id')
                            ->label('Assigned Coach')
                            ->relationship('coach', 'name')
                            ->searchable()
                            ->preload()
                            ->visible(fn() => auth()->user()->hasRole(['super_admin', 'admin'])),



                    ]),

                Section::make('Other Information')
                    ->schema([
                        DatePicker::make('registration_date')
                            ->required()
                            ->displayFormat('d/m/Y')
                            ->label('Registration Date')
                            ->placeholder('dd/mm/yyyy')
                            ->closeOnDateSelection(),

                        Toggle::make('active')
                            ->label('Active Status')
                            ->default(true)
                            ->onColor('success')
                            ->offColor('danger'),

                        Placeholder::make('balance_placeholder')
                            ->label('Balance')
                            ->content(fn(?Student $record): string => $record ? number_format($record->balance, 2) : '0.00'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->contentGrid([
                'md' => 2,
                'lg' => 3,
                'xl' => 4,
            ])
            ->headerActions([
                Tables\Actions\Action::make('import')
                    ->label('Import Students')
                    ->icon('heroicon-o-arrow-up-tray')
                    ->color('success')
                    ->form([
                        Forms\Components\FileUpload::make('file')
                            ->label('Excel File')
                            ->acceptedFileTypes([
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            ])
                            ->preserveFilenames()
                            ->directory('temp-imports')
                            ->required(),
                        Forms\Components\Toggle::make('has_header_row')
                            ->label('File has header row')
                            ->default(true),
                    ])
                    ->action(function (array $data): void {
                        try {
                            $path = storage_path('app/public/' . $data['file']);
                            Excel::import(new StudentsImport, $path);

                            // Cleanup
                            if (file_exists($path)) {
                                unlink($path);
                            }

                            Notification::make()
                                ->success()
                                ->title('Students imported successfully')
                                ->duration(5000)
                                ->send();

                        } catch (\Exception $e) {
                            \Log::error('Import error', [
                                'message' => $e->getMessage(),
                                'trace' => $e->getTraceAsString()
                            ]);

                            Notification::make()
                                ->danger()
                                ->title('Import Error')
                                ->body('Error importing students: ' . $e->getMessage())
                                ->duration(5000)
                                ->send();
                        }
                    })
                    ->modalWidth('md')
                    ->modalHeading('Import Students')
                    ->modalDescription('Upload an Excel file to import students. Make sure the file has the correct column headers.')
                    ->visible(auth()->user()->can('import students')),
            ])
            ->paginated([12, 24, 48])
            ->defaultPaginationPageOption(12)
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\Layout\Panel::make([
                        Tables\Columns\Layout\Grid::make(['default' => 1])
                            ->schema([
                                ImageColumn::make('profile_photo')
                                    ->disk('public')
                                    ->circular()
                                    ->size(100)
                                    ->defaultImageUrl(fn($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->english_name))
                                    ->alignCenter(),

                                TextColumn::make('english_name')
                                    ->alignCenter()
                                    ->weight('bold')
                                    ->size('xl')
                                    ->color('primary'),

                                TextColumn::make('position')
                                    ->alignCenter()
                                    ->weight('medium')
                                    ->color('warning')
                                    ->size('lg'),

                                Tables\Columns\Layout\Grid::make(['default' => 3])
                                    ->schema([
                                        Tables\Columns\Layout\Stack::make([
                                            TextColumn::make('age_label')
                                                ->label('AGE')
                                                ->state('AGE')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('sm')
                                                ->color('gray'),
                                            TextColumn::make('age')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('xl'),
                                        ]),

                                        Tables\Columns\Layout\Stack::make([
                                            TextColumn::make('height_label')
                                                ->label('HEIGHT')
                                                ->state('HEIGHT')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('sm')
                                                ->color('gray'),
                                            TextColumn::make('height')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('xl'),
                                        ]),

                                        Tables\Columns\Layout\Stack::make([
                                            TextColumn::make('weight_label')
                                                ->label('WEIGHT')
                                                ->state('WEIGHT')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('sm')
                                                ->color('gray'),
                                            TextColumn::make('weight')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('xl'),
                                        ]),
                                    ]),

                                Tables\Columns\Layout\Grid::make(['default' => 2])
                                    ->schema([
                                        Tables\Columns\Layout\Stack::make([
                                            TextColumn::make('level_label')
                                                ->label('LEVEL')
                                                ->state('LEVEL')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('sm')
                                                ->color('gray'),
                                            TextColumn::make('level_of_player')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('lg'),
                                        ]),

                                        Tables\Columns\Layout\Stack::make([
                                            TextColumn::make('category_label')
                                                ->label('CATEGORY')
                                                ->state('CATEGORY')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('sm')
                                                ->color('gray'),
                                            TextColumn::make('category')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('lg'),
                                        ]),

                                        Tables\Columns\Layout\Stack::make([
                                            TextColumn::make('attendance_label')
                                                ->label('ATTENDANCE')
                                                ->state('ATTENDANCE')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('sm')
                                                ->color('gray'),
                                            TextColumn::make('attendance_rate')
                                                ->formatStateUsing(fn($record) => number_format(
                                                    $record->attendances()->where('attended', true)->count()
                                                    / max($record->attendances()->count(), 1) * 100
                                                ) . '%')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('lg'),
                                        ]),

                                        Tables\Columns\Layout\Stack::make([
                                            TextColumn::make('classes_label')
                                                ->label('CLASSES')
                                                ->state('CLASSES')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('sm')
                                                ->color('gray'),
                                            TextColumn::make('classes_count')
                                                ->counts('classes')
                                                ->alignCenter()
                                                ->weight('bold')
                                                ->size('lg'),
                                        ]),
                                    ]),

                                Tables\Columns\Layout\Stack::make([
                                    TextColumn::make('balance_label')
                                        ->label('BALANCE')
                                        ->state('BALANCE')
                                        ->alignCenter()
                                        ->weight('bold')
                                        ->size('sm')
                                        ->color('gray'),
                                    TextColumn::make('balance')

                                        ->alignCenter()
                                        ->weight('bold')
                                        ->size('xl')
                                        ->color('success'),

                                ]),
                            ])
                    ])
                        ->collapsible()
                        ->collapsed(false)
                ])
            ])
            ->actions([
                // ...existing actions...
            ])

            ->defaultSort('english_name', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ClassesRelationManager::class,
            RelationManagers\PaymentsRelationManager::class,
            RelationManagers\AttendancesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStudents::route('/'),
            'create' => Pages\CreateStudent::route('/create'),
            'edit' => Pages\EditStudent::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'warning';
    }
}
