<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\StudentController;

// Public routes
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware(['auth:sanctum'])->group(function () {    
    Route::get('/profile', [StudentController::class, 'profile']);
    Route::get('/classes', [StudentController::class, 'classes']);
    Route::get('/attendances', [StudentController::class, 'attendances']);
    Route::get('/payments', [StudentController::class, 'payments']);
    Route::post('/logout', [AuthController::class, 'logout']);
});
