import { Badge } from './ui/badge';
import { Calendar, Wallet } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PaymentItemProps {
  date: string;
  amount: number;
  description: string;
  status: 'paid' | 'pending' | 'overdue';
}

const PaymentItem = ({ date, amount, description, status }: PaymentItemProps) => {
  const getStatusColor = () => {
    switch (status) {
      case 'paid':
        return 'bg-green-50 text-green-600 border-green-200';
      case 'pending':
        return 'bg-bball-gold/10 text-bball-gold border-bball-gold/20';
      case 'overdue':
        return 'bg-red-50 text-red-600 border-red-200';
      default:
        return 'bg-gray-50 text-gray-600 border-gray-200';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'paid':
        return 'Paid';
      case 'pending':
        return 'Pending';
      case 'overdue':
        return 'Overdue';
      default:
        return status;
    }
  };

  return (
    <div className="flex items-center justify-between border-b pb-3 last:border-0">
      <div className="flex items-center gap-3">
        <div className="h-10 w-10 rounded-full bg-bball-light-gray flex items-center justify-center">
          <Wallet className="h-5 w-5 text-bball-dark-purple" />
        </div>
        <div>
          <h4 className="text-sm font-medium text-bball-dark-purple">{description}</h4>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Calendar size={12} />
            <span>{date}</span>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-end gap-1">
        <span className="font-semibold">${amount.toFixed(2)}</span>
        <Badge variant="outline" className={cn("text-xs border", getStatusColor())}>
          {getStatusText()}
        </Badge>
      </div>
    </div>
  );
};

export default PaymentItem;
