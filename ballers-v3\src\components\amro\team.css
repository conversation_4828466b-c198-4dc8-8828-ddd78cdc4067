.sectionnnn {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 75em;
  margin-inline: auto;
  gap: 0.938rem;
  transform: translatey(-3em);
  position: relative;
  z-index: 10;
}

.card {
  position: relative;
  padding: 1.875em 1.25em;
  max-width: 18.75rem;
  width: 90%;
  height: 16.875rem;
  display: grid;
  place-content: center;
  place-items: center;
  text-align: center;
  border-radius: 1rem;
  background: linear-gradient(45deg, #1e272e, #48627a);
}

.card::before {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  right: 0.125rem;
  bottom: 0.125rem;
  background: #1f2c3a;
  border-radius: calc(1rem - 0.125rem);
  z-index: -1;
}

.card h2 {
  color: #fff;
  letter-spacing: 0.05rem;
  font-size: clamp(1rem, 0.9375rem + 0.3125vw, 1.25rem);
  margin-top: 0.625em;
  margin-bottom: 0.188em;
}

.card p {
  color: #afafaf;
  font-size: clamp(0.8rem, 0.7625rem + 0.1875vw, 0.95rem);
}

.card-top {
  flex-basis: 100%;
  display: flex;
  justify-content: center;
  gap: 0.938rem;
  flex-wrap: wrap;
}

.card-top .card img {
  width: clamp(7.5rem, 40vw, 9.375rem);
  height: clamp(7.5rem, 40vw, 9.375rem);
}

.card img {
  border-radius: 50%;
  width: clamp(100px, 40vw, 120px);
  height: clamp(100px, 40vw, 120px);
  object-fit: cover;
  object-position: top;
}