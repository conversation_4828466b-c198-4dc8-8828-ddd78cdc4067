<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AttendanceResource\Pages;
use App\Filament\Resources\AttendanceResource\RelationManagers;
use App\Models\Attendance;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\BooleanColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class AttendanceResource extends Resource
{
    protected static ?string $model = Attendance::class;
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';
    protected static ?string $navigationGroup = 'Training Management';
    protected static ?string $modelLabel = 'Attendance';
    protected static ?string $pluralModelLabel = 'Attendance Records';
    protected static ?int $navigationSort = 6;

    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('student_id')
                    ->label('Student')
                    ->relationship('student', 'english_name') // Assuming you have a Student model
                    ->searchable()
                    ->preload()
                    ->required(),

                Toggle::make('attended')
                    ->label('Present')
                    ->default(false),

                Textarea::make('notes')
                    ->label('Notes'),

                Forms\Components\Hidden::make('user_id')
                    ->default(Auth::id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                // Only apply coach filter for users with coach role
                // Super admin can see everything
                if (auth()->user()->hasRole('coach') && !auth()->user()->hasRole('super_admin')) {
                    $query->whereHas('student', function ($q) {
                        $q->where('coach_id', auth()->id());
                    });
                }
            })
            ->columns([
                

                TextColumn::make('student.english_name')
                    ->label('Student')
                    ->searchable()
                    ->sortable(),

                BooleanColumn::make('attended')
                    ->label('Present')
                    ->sortable(),

                TextColumn::make('notes')
                    ->label('Notes')
                    ->searchable(),

                TextColumn::make('user.name')->label('Created By')->searchable(),
                TextColumn::make('created_at')->label('Created At')->dateTime()->sortable(),
                TextColumn::make('updated_at')->label('Updated At')->dateTime()->sortable(),
            ])
            ->filters([
                

                SelectFilter::make('student_id')
                    ->relationship('student', 'english_name')
                    ->label('Student'),

                Filter::make('attended')
                    ->label('Present')
                    ->query(fn (Builder $query): Builder => $query->where('attended', true)),

                Filter::make('absent')
                    ->label('Absent')
                    ->query(fn (Builder $query): Builder => $query->where('attended', false)),
            ])
            ->actions([
                EditAction::make()->icon('heroicon-o-pencil-square'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAttendances::route('/'),
            'create' => Pages\CreateAttendance::route('/create'),
            'edit' => Pages\EditAttendance::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereDate('created_at', today())->count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'gray';
    }
}