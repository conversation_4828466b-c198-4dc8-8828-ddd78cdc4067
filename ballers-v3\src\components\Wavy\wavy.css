/* .main-wavy {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.wavy {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    
}

.waves {
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;} */


.main-wavy {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  /* border-start-start-radius: 20%; */
  border-end-end-radius: 2%;
    border-end-start-radius: 2%;

}

.wavy {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.waves {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
  z-index: 1;
}