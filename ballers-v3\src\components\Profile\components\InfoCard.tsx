import type { ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';

interface InfoCardProps {
  title: string;
  description?: string;
  children: ReactNode;
  className?: string;
}

const InfoCard = ({ title, description, children, className = '' }: InfoCardProps) => {
  return (
    <Card className={`shadow-md hover:shadow-lg transition-all duration-300 ${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-bold text-bball-dark-purple">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
};

export default InfoCard;
