.grid {
  display: grid;
  width: 100%;
}

/* Grid columns */
.grid--cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid--cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid--cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid--cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid--cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid--cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid--cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

/* Grid gaps */
.grid--gap-xs {
  gap: var(--space-1);
}

.grid--gap-sm {
  gap: var(--space-2);
}

.grid--gap-md {
  gap: var(--space-4);
}

.grid--gap-lg {
  gap: var(--space-6);
}

.grid--gap-xl {
  gap: var(--space-8);
}

/* Responsive classes */
@media (min-width: 640px) {
  .sm\:grid--cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .sm\:grid--cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .sm\:grid--cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .sm\:grid--cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .sm\:grid--cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  
  .sm\:grid--cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  
  .sm\:grid--cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .md\:grid--cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .md\:grid--cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .md\:grid--cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .md\:grid--cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .md\:grid--cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  
  .md\:grid--cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  
  .md\:grid--cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid--cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .lg\:grid--cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .lg\:grid--cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .lg\:grid--cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .lg\:grid--cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  
  .lg\:grid--cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  
  .lg\:grid--cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .xl\:grid--cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .xl\:grid--cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .xl\:grid--cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .xl\:grid--cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .xl\:grid--cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  
  .xl\:grid--cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  
  .xl\:grid--cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}