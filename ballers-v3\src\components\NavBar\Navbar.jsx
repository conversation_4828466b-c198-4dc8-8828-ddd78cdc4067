import React, { useState, useEffect } from 'react';
import './Navbar.css';
import logo from '../images/ballers_logo.png';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLanguage } from '@fortawesome/free-solid-svg-icons';
import { useTranslation } from 'react-i18next';

function Navbar() {
    const [activeNavItem, setActiveNavItem] = useState('Home');
    const [isOpen, setIsOpen] = useState(false);
    const { t, i18n } = useTranslation();

    // Handle nav item click
    const handleNavItemClick = (itemName) => {
        setActiveNavItem(itemName);
        setIsOpen(false);
    };

    // Toggle sidebar
    const handleMenuClick = () => {
        setIsOpen(!isOpen);
    };

    // Close sidebar when clicking overlay
    const handleOverlayClick = () => {
        setIsOpen(false);
    };

    // Update document direction on language change
    useEffect(() => {
        document.documentElement.dir = i18n.dir();
    }, [i18n.language]);

    return (
        <nav className={isOpen ? 'open' : ''}>
            <div className="logo">
                <div className="containNav">
                    {/* Mobile Menu Button */}
                    <div className="boxNav">
                        <i className="fa-solid fa-bars" id='none' onClick={handleMenuClick}></i>
                        <a href='./'><img src={logo} alt="Ballers Logo" /></a>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="boxNav display">
                        <ul className="navbar-bar">
                            <li className={`bar-item ${activeNavItem === 'Home' ? 'active' : ''}`} onClick={() => handleNavItemClick('Home')}>
                                <a className="bar-link" href="./">{t('Home-Nav')}</a>
                            </li>
                            <li className={`bar-item ${activeNavItem === 'About' ? 'active' : ''}`} onClick={() => handleNavItemClick('About')}>
                                <a className="bar-link" href="./about">{t('About-Nav')}</a>
                            </li>
                            <li className={`bar-item ${activeNavItem === 'Team' ? 'active' : ''}`} onClick={() => handleNavItemClick('Team')}>
                                <a className="bar-link" href="./team">{t('Team-Nav')}</a>
                            </li>
                            <li className={`bar-item ${activeNavItem === 'Gallery' ? 'active' : ''}`} onClick={() => handleNavItemClick('Gallery')}>
                                <a className="bar-link" href="./gallery">{t('Gallery-Nav')}</a>
                            </li>
                            <li className="bar-item">|</li>
                            {/* Language Switcher */}
                            <li className="bar-item lang-switcher">
                                <FontAwesomeIcon icon={faLanguage} />
                                <select
                                    className="selectNav"
                                    onChange={(e) => {
                                        const selectedLang = e.target.value;
                                        i18n.changeLanguage(selectedLang);
                                        localStorage.setItem('language', selectedLang);
                                    }}
                                    defaultValue={i18n.language}
                                >
                                    <option value="ar">{t('Lang-Nav-Arabic')}</option>
                                    <option value="en">{t('Lang-Nav-English')}</option>
                                </select>
                            </li>
                        </ul>
                    </div>

                    {/* Auth Buttons - Desktop */}
                    <div className="boxNav auth-buttons">
                        <a href='./join' className="join-link">
                            <button className='btn-Nav login-btn'>{t('Join-Nav')}</button>
                        </a>
                        <a href='./login' className="join-link">
                            <button className='btn-Nav join-btn'>{t('Login-Nav')}</button>
                        </a>
                    </div>

                    {/* Auth Buttons - Mobile */}
                    <div className="mobile-auth-buttons">
                        <a href='./login' className="join-link">
                            <button className='btn-Nav login-btn'>{t('Login-Nav')}</button>
                        </a>
                        <a href='./join' className="join-link">
                            <button className='btn-Nav join-btn'>{t('Join-Nav')}</button>
                        </a>
                    </div>
                </div>
            </div>

            {/* Sidebar Menu */}
            <div className="sidebar">
                <div className="logo">
                    <i className="fa-solid fa-xmark" onClick={handleOverlayClick}></i>
                    <img src={logo} alt="Ballers Logo" id='sidebar-logo' />
                </div>
                <div className="sidebar-content">
                    <ul className="lists">
                        <li className="list">
                            <a href="./" className="nav-link" onClick={handleOverlayClick}>
                                <span className="link">{t('Home-Nav')}</span>
                            </a>
                        </li>
                        <li className="list">
                            <a href="./about" className="nav-link" onClick={handleOverlayClick}>
                                <span className="link">{t('About-Nav')}</span>
                            </a>
                        </li>
                        <li className="list">
                            <a href="./team" className="nav-link" onClick={handleOverlayClick}>
                                <span className="link">{t('Team-Nav')}</span>
                            </a>
                        </li>
                        <li className="list">
                            <a href="./gallery" className="nav-link" onClick={handleOverlayClick}>
                                <span className="link">{t('Gallery-Nav')}</span>
                            </a>
                        </li>
                        <li className="list lang-switcher-sidebar">
                            <FontAwesomeIcon icon={faLanguage} />
                            <select
                                className="selectNav"
                                onChange={(e) => {
                                    const selectedLang = e.target.value;
                                    i18n.changeLanguage(selectedLang);
                                    localStorage.setItem('language', selectedLang);
                                }}
                                defaultValue={i18n.language}
                            >
                                <option value="ar">{t('Lang-Nav-Arabic')}</option>
                                <option value="en">{t('Lang-Nav-English')}</option>
                            </select>
                        </li>
                    </ul>

                    {/* Sidebar Auth Buttons */}
                    <div className="sidebar-auth-buttons">
                        <a href='./login' className="join-link">
                            <button className='btn-Nav login-btn'>{t('Login-Nav')}</button>
                        </a>
                        <a href='./join' className="join-link">
                            <button className='btn-Nav join-btn'>{t('Join-Nav')}</button>
                        </a>
                    </div>
                </div>
            </div>

            {/* Overlay */}
            <div className="overlay" onClick={handleOverlayClick}></div>
        </nav>
    );
}

export default Navbar;