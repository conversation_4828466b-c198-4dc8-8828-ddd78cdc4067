<?php

namespace App\Filament\Resources\SessionnResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AttendancesRelationManager extends RelationManager
{
    protected static string $relationship = 'attendances';
    protected static ?string $title = 'Attendance';
    
    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitle('Attendance')
            ->heading('Class Attendance')
            ->description(fn () => "Session Date: " . $this->getOwnerRecord()->sessionn_date->format('d/m/Y'))
            ->contentGrid([
                'xl' => 4,
            ])
            ->modifyQueryUsing(function (Builder $query) {
                // If coach and not super admin, only show their students
                if (auth()->user()->hasRole('coach') && !auth()->user()->hasRole('super_admin')) {
                    $query->whereHas('student', function ($q) {
                        $q->where('coach_id', auth()->id());
                    });
                }
            })
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\ImageColumn::make('student.profile_photo_path')
                        ->circular()
                        ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->student?->english_name ?? '')),
                    
                    Tables\Columns\TextColumn::make('student.english_name')
                        ->searchable(['students.english_name']),
                    
                    Tables\Columns\ToggleColumn::make('attended')
                        ->alignCenter()
                        ->onColor('success')
                        ->offColor('danger'),
                ]),
            ])
            ->filters([])
            ->headerActions([
                Tables\Actions\Action::make('mark_all_present')
                    ->action(function () {
                        $query = $this->getOwnerRecord()->attendances();
                        
                        // If coach and not super admin, only affect their students
                        if (auth()->user()->hasRole('coach') && !auth()->user()->hasRole('super_admin')) {
                            $query->whereHas('student', function ($q) {
                                $q->where('coach_id', auth()->id());
                            });
                        }
                        
                        $query->update(['attended' => true]);
                    })
                    ->color('success')
                    ->icon('heroicon-o-check'),
                
                Tables\Actions\Action::make('mark_all_absent')
                    ->action(function () {
                        $query = $this->getOwnerRecord()->attendances();
                        
                        // If coach and not super admin, only affect their students
                        if (auth()->user()->hasRole('coach') && !auth()->user()->hasRole('super_admin')) {
                            $query->whereHas('student', function ($q) {
                                $q->where('coach_id', auth()->id());
                            });
                        }
                        
                        $query->update(['attended' => false]);
                    })
                    ->color('danger')
                    ->icon('heroicon-o-x-mark'),
            ])
            ->actions([])
            ->bulkActions([]);
    }

    public function afterMount(): void
    {
        $sessionId = $this->ownerRecord->id;
        $classId = $this->ownerRecord->class_id;
        
        // Get students based on coach assignment for users with coach role
        // Super admin can see all students
        $studentsQuery = \App\Models\Student::query();
        
        if (auth()->user()->hasRole('coach') && !auth()->user()->hasRole('super_admin')) {
            $studentsQuery->where('coach_id', auth()->id());
        }
        
        $students = $studentsQuery
            ->whereHas('classes', function ($query) use ($classId) {
                $query->where('class_id', $classId)->where('is_active', true);
            })
            ->get();
            
        // Create attendance records for these students if they don't exist
        foreach ($students as $student) {
            \App\Models\Attendance::firstOrCreate([
                'sessionn_id' => $sessionId,
                'student_id' => $student->id,
            ], [
                'attended' => false,
                'user_id' => auth()->id(),
            ]);
        }
    }
}
