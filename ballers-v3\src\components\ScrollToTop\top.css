/* ScrollToTopButton.css */
.scroll-to-top {
  position: fixed;
  bottom: 90px;
  right: 25px;
  background-color: #DC552C;
  color: #000000;
  font-weight: bold;
  font-family: <PERSON><PERSON>, <PERSON><PERSON><PERSON>;
  cursor: pointer;
  border-radius: 50%;
  padding: 12px; /* Replaced padding-inline with standard padding */
  display: none;
  z-index: 2;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* Transition properties with proper prefix order */
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.scroll-to-top:hover {
  background-color: #404969;
  color: #000000;
}

.scroll-to-top.show {
  display: flex;
}

/* Icon styling */
.scroll-to-top svg {
  color: #FFFFFF;
  width: 20px;
  height: 20px;
}

/* Scroll bar styles - improved compatibility */
.sidebar {
  /* Webkit browsers (Chrome, Safari) */
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: #f6f6f6;
    box-shadow: 0 0 10px #ddd inset;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #6c0202;
    border-radius: 0px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background-color: #7a5b5b;
  }
  
  /* Firefox */
  /* scrollbar-width: thin;
  scrollbar-color: #6c0202 #f6f6f6; */
}

/* Fallback for older browsers */
@supports not (scrollbar-width: thin) {
  .sidebar {
    /* Alternative styling for browsers without scrollbar customization */
    overflow-y: auto;
  }
}