




.contact_bg .text {
    margin: 1.6rem 0;
}
.contact_body {
    max-width: 1120px;
    margin: 0 auto 5rem auto;
    padding: 0 1rem;
    font-family: 'Open Sans', sans-serif;
}


.contact_info {
    margin: 2rem 0;
    text-align: center;
    padding: 2rem 0;
    display: grid;
    grid-template-columns: repeat(4 , 1fr);
    font-family: 'Open Sans', sans-serif;
}
.info_box {
    text-align: center;
    padding: 20px 6px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    background: transparent;
    transition: transform 0.5s ,background 0.5s;
    margin-right: 3px;
    margin-left: 3px;
}
.info_box:hover {
    background: #DC552C;
    color: rgb(0, 0, 0);
    transform: scale(1);
    opacity: 0.5;
}

.info_box:hover span  {
    color: rgb(3, 3, 3);
}
.info_box:hover i  {
    color: white;
}
.contact_info span {
 display: block;
}

.contact_info div {
    margin: 0.8rem 0;
    padding: 1rem;
}
.contact_info span i {
    font-size: 2rem;
    padding-bottom: 0.9rem;
    color: #74a7a1;
}
.contact_info div span:nth-child(3) {
    margin: 0.7rem 0;
}

.contact_info div span:nth-child(2) {
    font-weight: 500;
    font-size: 1.1rem;
}

.contact_info text {
    padding-top: 0.4rem;
}

.contact_form {
    padding: 2rem 0;
    margin-top: 60px;
}
.contact_form form {
    padding-bottom: 1rem;
}
.form_control {
    border: none;
    width: 100%;
    border-radius: 5px;
    padding: 0.7rem;
    margin: 1.3rem 0;
    font-size: 1rem;
    outline: 0;
    background-color: #c9ebf9;
}
.form_control:focus {
    box-shadow: 0 0 9px -3px #1a1e2b;
}

.send_btn {
    font-family:'open sans' sans-serif; 
    font-size: 1rem;
    text-transform: uppercase;
    color: #fff;
    background-color: #DC552C;
    border: none;
    border-radius: 5px;
    padding: 0.7rem 1.5rem;
    cursor: pointer;
    transition: all 0.4s ease;
}

.send_btn:hover {
    background-color: #404969;
}
.contact_form div img {
    width:100%;
    height: 70vh;
}
.contact_form div {
    margin: 0 auto;
    text-align: center;
}

@media (max-width: 768px) {
    .contact_body .contact_info {
        display: flex;
        flex-direction: column;
    }
    .contact_form {
        display: flex;
        flex-direction: column;
    }
   
}

