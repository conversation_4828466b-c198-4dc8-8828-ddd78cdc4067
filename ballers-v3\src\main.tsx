import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import "bootstrap/dist/css/bootstrap.min.css";
import './App.css'; // Ensure this file exists
import './index.css'; // Ensure this file exists
import './i18n'; // If using i18n for translations

import { createBrowserRouter, RouterProvider } from 'react-router-dom';

// Import pages/components for routing
import ErrorPage from './pages/errorPage';
import Home from './pages/home';
import Team from './pages/team';
import About from './pages/about';
import JoinUs from './pages/JoinUs';
import Login from './components/authh/login';
import Amro from './personal/Amro/amro';
import Ahmad from './personal/Ahmad/ahmad';
import Hussien from './personal/Hussien/hussien';
import Laith from './personal/Laith/LaithS';
import Rania from './personal/Rania/RaniaS';
import Shahd from './personal/Shahd/ShahdS';
import Gallery from './pages/gallery';
import Profile from './player/Player';

// Create the router for navigation
const router = createBrowserRouter([
  { path: '/', element: <Home />, errorElement: <ErrorPage /> },
  { path: '/team', element: <Team /> },
  { path: '/gallery', element: <Gallery /> },
  { path: '/Amro', element: <Amro /> },
  { path: '/Ahmad', element: <Ahmad /> },
  { path: '/Hussien', element: <Hussien /> },
  { path: '/Laith', element: <Laith /> },
  { path: '/Rania', element: <Rania /> },
  { path: '/Shahd', element: <Shahd /> },
  { path: '/about', element: <About /> },
  { path: '/join', element: <JoinUs /> },
  { path: '/login', element: <Login /> },
  { path: '/profile', element: <Profile /> },
]);

// Use createRoot for React 18+ to render the app
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>
);
