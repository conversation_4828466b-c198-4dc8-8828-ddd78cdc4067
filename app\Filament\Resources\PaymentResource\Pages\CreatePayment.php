<?php

namespace App\Filament\Resources\PaymentResource\Pages;

use App\Filament\Resources\PaymentResource;
use App\Models\Payment;
use App\Models\Student;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CreatePayment extends CreateRecord
{
    protected static string $resource = PaymentResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            $payment = Payment::create($data);
            $student = Student::findOrFail($data['student_id']);
            DB::table('students')
                ->where('id', $student->id)
                ->update([
                    'balance' => DB::raw("balance + ({$data['amount']})")
                ]);
            return $payment;
        });
    }

    protected function afterCreate(): void
    {
        $student = Student::find($this->record->student_id);
        $student->refresh();

        Notification::make()
            ->title('Payment Processed')
            ->body("New balance: JD {$student->balance}")
            ->success()
            ->send();
    }
}