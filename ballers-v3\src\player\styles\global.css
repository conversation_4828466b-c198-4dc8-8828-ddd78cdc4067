@import './reset.css';
@import './variables.css';
@import './utilities.css';

body {
  font-family: var(--font-family-primary);
  color: var(--color-neutral-900);
  background-color: var(--color-neutral-50);
}

/* Profile Cover */
.profile-cover {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.profile-cover__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-cover__change-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius-full);
  padding: 0.5rem 1rem;
}


/* Profile Header */
.profile-header {
  margin-top: -4rem;
  margin-bottom: 2rem;
  padding: 0 1rem;
}

.profile-header__main {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

/* Profile Avatar */
.profile-avatar {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 1px solid rgb(211, 99, 47);
  overflow: hidden;
  background-color: #6B4EE6;
  box-shadow: var(--shadow-md);
  margin-top: 2rem;
}

.profile-avatar__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-avatar__change-btn {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  background-color: rgba(248, 119, 73, 0.9);
  border-radius: 50%;
  padding: 0.5rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Profile Info */
.profile-info {
  padding-bottom: 1rem;
  text-align: center;
}

.profile-info__name {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-900);
  margin: 0;
}

.profile-info__number {
  font-size: 1.5rem;
  color: var(--color-primary-600);
  font-weight: var(--font-weight-bold);
}

.profile-info__meta {
  display: flex;
  gap: 0.50rem;
  align-items: center;
  justify-content: center;
  color: var(--color-neutral-600);
  font-size: var(--font-size-sm);
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

/* Medium screens and up */
@media (min-width: 768px) {
  .profile-header__main {
    flex-direction: row;
    align-items: flex-end;
    gap: 2rem;
  }
  
  .profile-info {
    text-align: left;
    padding-bottom: 1rem;
  }
  
  .profile-info__meta {
    justify-content: flex-start;
  }
  
  .profile-avatar {
    width: 180px;
    height: 180px;
    margin-top: 0;
  }
}
/* Mobile styles (under 480px) */
@media (max-width: 480px) {
  .profile-info__top {
    flex-direction: column;
    align-items: center;
    gap: 0.3rem !important;
  }
  .profile-info__name {
  font-size: 1.8rem;

}

.profile-info__number {
  font-size: 1.3rem;
}
  
  .profile-info__name-number {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .profile-badges {
    justify-content: center;
    width: 100%;
  }
}

/* Profile Badges */
.profile-badges {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--color-neutral-100);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
}

/* Personal Info */
.personal-info {
  display: grid;
  gap: 1rem;
}

.personal-info__item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--color-neutral-100);
}

.personal-info__item:last-child {
  border-bottom: none;
}

.personal-info__label {
  color: var(--color-neutral-600);
}

.personal-info__value {
  font-weight: var(--font-weight-medium);
}

/* Achievements */
.achievements {
  display: grid;
  gap: 1rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--color-neutral-50);
  border-radius: var(--border-radius-lg);
}

.achievement-item__icon {
  color: var(--color-primary-600);
}

.achievement-item__title {
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.achievement-item__date {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
}

/* Profile Content */
.profile-content {
  padding: 2rem 1rem;
}

@media (min-width: 768px) {
  .profile-content {
    padding: 2rem 0;
  }
}

/* Announcements */
.announcements {
  display: grid;
  gap: 1.5rem;
}

.announcement-item {
  padding: 1.5rem;
  background-color: var(--color-neutral-50);
  border-radius: var(--border-radius-lg);
}

.announcement-item__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.announcement-item__title {
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-900);
}

.announcement-item__date {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
}

.announcement-item__content {
  color: var(--color-neutral-700);
  line-height: var(--line-height-relaxed);
}

/* Performance Stats */
.performance-stat {
  padding: 1.5rem;
  background-color: var(--color-neutral-50);
  border-radius: var(--border-radius-lg);
  text-align: center;
}

.performance-stat__title {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
  margin-bottom: 0.5rem;
}

.performance-stat__values {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.performance-stat__main {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-900);
}

.performance-stat__trend {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-full);
}

.performance-stat__trend--up {
  background-color: var(--color-success-50);
  color: var(--color-success-600);
}

.performance-stat__trend--down {
  background-color: var(--color-error-50);
  color: var(--color-error-600);
}

/* Media Grid */
.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.media-item {
  position: relative;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  aspect-ratio: 1;
}

.media-item__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-item__overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.media-item__title {
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.25rem;
}

.media-item__date {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* Resources */
.resources {
  display: grid;
  gap: 1rem;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--color-neutral-50);
  border-radius: var(--border-radius-lg);
}

.resource-item__icon {
  color: var(--color-primary-600);
  margin-right: 1rem;
}

.resource-item__info {
  flex: 1;
}

.resource-item__title {
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.25rem;
}

.resource-item__meta {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
}