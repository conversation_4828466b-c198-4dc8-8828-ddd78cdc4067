import React from 'react';
import { useTranslation } from 'react-i18next';

import BallersLogo from "../images/ballers_logo.png";
import Linkedin from '../images/footer1.png';
import Facebook from '../images/footer2.png'; // Corrected name
import Instagram from '../images/footer3.png'; // Corrected name

import "./Footer.css";

function Footer() {
    const { t } = useTranslation();

    return (
        <footer>
            <div className="content">
                <div className="link-boxes">
                    {/* Logo Section */}
                    <ul className="box">
                        <li className="logo-footer">
                            <img src={BallersLogo} alt="Ballers Logo" width="100" height="100" />
                        </li>
                        <li className="address">
                            {t('Footer.location1')}<br />
                            {t('Footer.location2')}
                        </li>
                    </ul>

                    {/* Box 1 - About Links */}
                    <ul className="box">
                        <li className="link_name">{t('Footer.Box1.link1')}</li>
                        <li><a href="./team">{t('Footer.Box1.link2')}</a></li>
                        <li><a href="./team">{t('Footer.Box1.link3')}</a></li>
                        <li><a href="./team">{t('Footer.Box1.link4')}</a></li>
                        <li><a href="./team">{t('Footer.Box1.link5')}</a></li>
                    </ul>

                    {/* Box 2 - Join / Learn */}
                    <ul className="box">
                        <li className="link_name">{t('Footer.Box2.link1')}</li>
                        <li><a href="./team">{t('Footer.Box2.link2')}</a></li>
                        <li><a href="./join">{t('Footer.Box2.link3')}</a></li>
                        <li><a href="./team">{t('Footer.Box2.link4')}</a></li>
                    </ul>

                    {/* Box 3 - Contact / Pages */}
                    <ul className="box">
                        <li className="link_name">{t('Footer.Box3.link1')}</li>
                        <li><a href="./join">{t('Footer.Box3.link2')}</a></li>
                        <li><a href="./team">{t('Footer.Box3.link3')}</a></li>
                        <li><a href="./about">{t('Footer.Box3.link4')}</a></li>
                    </ul>

                    {/* Newsletter + Social Media */}
                    <ul className="box input-box">
                        <li className="link_name">{t('Footer.Box4.link1')}</li>
                        <div className="media-icon">
                            <a href="https://www.linkedin.com/in/... " target="_blank" rel="noopener noreferrer">
                                <img src={Linkedin} alt="LinkedIn" />
                            </a>
                            <a href="https://www.facebook.com/... " target="_blank" rel="noopener noreferrer">
                                <img src={Facebook} alt="Facebook" />
                            </a>
                            <a href="https://www.instagram.com/... " target="_blank" rel="noopener noreferrer">
                                <img src={Instagram} alt="Instagram" />
                            </a>
                        </div>

                        <li>
                            <div className="input-wrapper">
                                <input type="email"
                                       placeholder={t('enterYourEmailFooter')}
                                       aria-label={t('enterYourEmailFooter')} />
                                <button type="submit">{t('subscribeButton')}</button>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            {/* Bottom Copyright Text */}
            <div className="bottom-details">
                {t('Footer.Copy')}
            </div>
        </footer>
    );
}

export default Footer;