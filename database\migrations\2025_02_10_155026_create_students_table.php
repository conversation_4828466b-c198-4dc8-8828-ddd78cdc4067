<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->string('profile_photo')->nullable();
            $table->string('arabic_name')->nullable();
            $table->string('english_name')->nullable();
            $table->string('gender')->nullable();
            $table->date('dob')->nullable();
            $table->string('email')->unique()->nullable(); // Make sure email is unique
            $table->string('password')->nullable();
            $table->string('age')->nullable();
            $table->string('parent_number')->nullable();
            $table->string('level_of_player')->nullable();
            $table->string('position')->nullable();
            $table->string('category')->nullable();
            $table->string('weight')->nullable();
            $table->string('height')->nullable();
            $table->string('school_name')->nullable();
            $table->string('instagram')->nullable();
            $table->string('facebook')->nullable();
            $table->boolean('active')->default(true);
            $table->decimal('balance', 10, 2)->default(0.00)->nullable(); 
            $table->foreignId('coach_id')->nullable()->constrained('users')->nullOnDelete();

            $table->date('registration_date')->nullable()->default(now());
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};