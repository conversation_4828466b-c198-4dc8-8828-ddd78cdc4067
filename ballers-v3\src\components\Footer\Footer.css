/* Footer Base Styles */
footer {
  background-color: #ffffff;
  width: 100%;
  color: #404969;
  font-weight: 500;
  box-shadow: 1px 6px 9px 0px rgba(0, 0, 0, 0.2);
}

footer .content {
  max-width: 1250px;
  margin: auto;
  padding: 30px 40px 40px 40px;
}

footer .content .link-boxes {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

/* Box List Styling */
.link-boxes .box {
  min-width: 180px;
  flex: 1;
}

/* Logo Section */
.logo-footer img {
  width: 100px;
  height: 100px;
}

.address {
  font-size: 13px;
  line-height: 1.5;
  margin-top: 10px;
}

/* Link Title (e.g., "Quick Links") */
.link_name {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  font-weight: 700;
  line-height: 19px;
  letter-spacing: 0em;
  margin-bottom: 10px;
  position: relative;
  color: #404969;
}

/* Language Direction Adjustments */
div[lang="ar"] .link_name {
  text-align: left;
  color: #404969;
  font-size: 18px;
  font-weight: 500;
}
div[lang="en"] .link_name {
  text-align: right;
  color: #cd2121;
}

/* Underline for link titles */
.link_name::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -5px;
  height: 2px;
  width: 35px;
  background-color: #d16459;
}

/* Footer Links */
.link-boxes .box li {
  list-style: none;
  margin: 15px 0;
}

.link-boxes .box li a {
  text-decoration: none;
  color: #404969;
  font-size: 14px;
  font-weight: 500;
  opacity: 0.8;
  transition: all 0.4s ease;
}

.link-boxes .box li a:hover {
  opacity: 1;
  text-decoration: underline;
}

/* Newsletter Input Section */
.input-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.input-wrapper input[type="email"] {
  flex: 1;
  padding: 6px 10px;
  border: none;
  border-bottom: 1px solid #404969;
  background: transparent;
  color: #404969;
  font-size: 13px;
  outline: none;
}

.input-wrapper input::placeholder {
  color: #404969;
  font-family: 'Lato';
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
}

.input-wrapper button {
  background-color: transparent;
  border: 2px solid #d16459;
  color: #d16459;
  padding: 5px 12px;
  cursor: pointer;
  font-weight: bold;
  font-size: 13px;
  transition: all 0.3s ease;
}

.input-wrapper button:hover {
  background-color: #d16459;
  color: #fff;
}

/* Social Media Icons */
.media-icon {
  display: flex;
  align-items: center;
  margin-top: 10px;
  gap: 10px;
}

.media-icon img {
  width: 30px;
  height: 30px;
  transition: transform 0.3s ease;
}

.media-icon img:hover {
  transform: scale(1.1);
}

/* Bottom Details (Copyright) */
.bottom-details {
  text-align: center;
  font-family: 'Lato';
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  color: #DC552C;
  opacity: 0.8;
  padding-bottom: 32px;
  transition: opacity 0.3s ease;
}

.bottom-details:hover {
  opacity: 1;
}

/* Responsive Styles */
@media (max-width: 900px) {
  .link-boxes {
    flex-direction: column;
    align-items: flex-start;
  }
  .input-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
  .media-icon {
    margin-top: 10px;
  }
}

@media (max-width: 700px) {
  footer .content {
    padding: 20px;
  }
  .media-icon {
    margin-top: 10px;
  }
}

@media (max-width: 540px) {
  .media-icon {
    margin-top: 10px;
  }
  .input-wrapper input,
  .input-wrapper button {
    width: 100%;
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* RTL Enhancements */
[dir="rtl"] .media-icon {
  flex-direction: row-reverse;
}

[dir="rtl"] .link_name::before {
  left: auto;
  right: 0;
}

[dir="rtl"] .input-wrapper {
  flex-direction: row-reverse;
}

[dir="rtl"] .input-wrapper input {
  text-align: right;
}

[dir="rtl"] .address {
  text-align: right;
}