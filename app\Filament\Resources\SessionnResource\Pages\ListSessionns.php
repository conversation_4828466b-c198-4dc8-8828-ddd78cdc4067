<?php

namespace App\Filament\Resources\SessionnResource\Pages;

use App\Filament\Resources\SessionnResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSessionns extends ListRecords
{
    protected static string $resource = SessionnResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
