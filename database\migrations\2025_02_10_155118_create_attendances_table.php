<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
      Schema::create('attendances', function (Blueprint $table) {
          $table->id();
          $table->foreignId('sessionn_id')->constrained()->onDelete('cascade');
          $table->foreignId('student_id')->constrained()->onDelete('cascade');
          $table->boolean('attended')->default(false);
           $table->text('notes')->nullable();
          $table->timestamps();
          $table->unique(['sessionn_id', 'student_id']); // Prevent duplicate attendance
           });
    }

    public function down(): void
    {
        Schema::dropIfExists('attendances');
    }
};