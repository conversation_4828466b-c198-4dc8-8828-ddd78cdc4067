<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SessionnResource\Pages;
use App\Filament\Resources\SessionnResource\RelationManagers;
use App\Models\Sessionn;
use App\Models\ClassModel;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\DateRangeFilter;

class SessionnResource extends Resource
{
    protected static ?string $model = Sessionn::class;
    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';
    protected static ?string $navigationGroup = 'Training Management';
    protected static ?string $modelLabel = 'Session';
    protected static ?string $pluralModelLabel = 'Sessions';
    protected static ?int $navigationSort = 4;

    protected static ?string $navigationLabel = 'Sessions';
    protected static ?string $recordTitleAttribute = 'sessionn_date';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('class_id')
                    ->label('Class')
                    ->relationship('class', 'class_name')
                    ->required()
                    ->searchable() // Make searchable
                    ->preload(),   // Preload for better performance
                DatePicker::make('sessionn_date')
                    ->required()
                    ->displayFormat('d/m/Y')  // User-friendly format
                    ->closeOnDateSelection(),
                TimePicker::make('start_time')
                    ->required()
                    ->seconds(false)
                    ->displayFormat('h:i A'),
                TimePicker::make('end_time')
                    ->required()
                    ->seconds(false)
                    ->displayFormat('h:i A'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('class.class_name')->sortable(), // Access related data
                TextColumn::make('sessionn_date')->date()->sortable(),
                TextColumn::make('start_time')->time(),
                TextColumn::make('end_time')->time(),
                Tables\Columns\TextColumn::make('attendances_count')
                    ->counts('attendances')
                    ->label('Attendance')
                    ->description(fn ($record) => $record->attendances()->where('attended', true)->count() . ' present'),
            ])
            ->filters([
                SelectFilter::make('class_id')
                    ->label('Filter by Class')
                    ->options(ClassModel::all()->pluck('class_name', 'id'))
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
        
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\AttendancesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSessionns::route('/'),
            'create' => Pages\CreateSessionn::route('/create'), //allow create
            'edit' => Pages\EditSessionn::route('/{record}/edit'), //allow edit
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        // Remove all filtering - everyone can see all sessions
        return parent::getEloquentQuery();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::whereDate('sessionn_date', today())->count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'primary';
    }

}