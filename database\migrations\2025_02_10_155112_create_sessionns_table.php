<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
      Schema::create('sessionns', function (Blueprint $table) {
          $table->id();
          $table->foreignId('class_id')->constrained('class_models')->onDelete('cascade'); //FK
          $table->date('sessionn_date');
          $table->time('start_time');
          $table->time('end_time');
          $table->timestamps();
      });
    }

    public function down(): void
    {
        Schema::dropIfExists('sessionns');
    }
};