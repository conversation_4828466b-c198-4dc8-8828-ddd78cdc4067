import { useEffect } from 'react';

const AI = () => {
  useEffect(() => {
    // Check if script is already added
    if (document.getElementById('chatbase-loader')) return;

    const inlineScript = document.createElement('script');
    inlineScript.id = 'chatbase-loader';
    inlineScript.innerHTML = `
      (function(){
        if(!window.chatbase || window.chatbase("getState") !== "initialized"){
          window.chatbase = (...arguments) => {
            if(!window.chatbase.q) {
              window.chatbase.q = [];
            }
            window.chatbase.q.push(arguments);
          };
          window.chatbase = new Proxy(window.chatbase, {
            get(target, prop){
              if(prop === "q"){ return target.q }
              return (...args) => target(prop, ...args);
            }
          });
        }

        const onLoad = function(){
          const script = document.createElement("script");
          script.src = "https://www.chatbase.co/embed.min.js";
          script.id = "m2bBCy5FDi7MKy3BxKyi-";
          script.domain = "www.chatbase.co";
          document.body.appendChild(script);
        };

        if(document.readyState === "complete"){
          onLoad();
        } else {
          window.addEventListener("load", onLoad);
        }
      })();
    `;
    document.body.appendChild(inlineScript);
  }, []);

  return null; // This component does not render anything
};
export default AI