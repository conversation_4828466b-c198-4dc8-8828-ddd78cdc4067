<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClassModelResource\Pages;
use App\Filament\Resources\ClassModelResource\RelationManagers;
use App\Models\ClassModel;
use App\Models\User; 
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Section; 

class ClassModelResource extends Resource
{
    protected static ?string $model = ClassModel::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Training Management';
    protected static ?string $modelLabel = 'Class';
    protected static ?string $pluralModelLabel = 'Classes';
    protected static ?int $navigationSort = 3;

    protected static ?string $navigationLabel = 'Classes';
    protected static ?string $recordTitleAttribute = 'class_name';

   public static function form(Form $form): Form
      {
        return $form
            ->schema([
                Section::make('Class Details')
                ->description('Information about the class')
                ->icon('heroicon-m-information-circle')
                ->columns(2)
                ->collapsible()
                ->schema([

                TextInput::make('class_name')
                    ->required()
                    ->maxLength(255)
                   ->placeholder('Enter class name'),
                  Textarea::make('description')
                    ->columnSpanFull() 
                    ->placeholder('Enter class description')
                  ->rows(3),
                   Select::make('day_of_week')
                   
                    ->options([
                        1 => 'Monday',
                        2 => 'Tuesday',
                        3 => 'Wednesday',
                        4 => 'Thursday',
                        5 => 'Friday',
                       6 => 'Saturday',
                      7 => 'Sunday',
                    ])
                    ->required()
                  ->native(false) 
                    ->placeholder('Select a day'),
                  TimePicker::make('start_time')
                   ->required()
                   ->seconds(false)
                   ->displayFormat('h:i A') 
                  ->placeholder('Select start time'),
                 TimePicker::make('end_time')
                    ->required()
                   ->seconds(false)
                    ->displayFormat('h:i A') // 12-hour format
                     ->placeholder('Select end time'),

                   Select::make('instructor_id')
                       ->label('Instructor')
                     ->relationship('instructor', 'name') 
                      //->options(User::all()->pluck('name', 'id')) //Alternative
                      ->searchable()
                       ->preload()
                       ->placeholder('Select an instructor')
                      ->nullable(),
                  TextInput::make('capacity')
                      ->required()
                     ->numeric()
                    ->placeholder('Enter class capacity')
                   ->minValue(1), // Add validation
                  TextInput::make('price_per_month')
                    ->numeric()
                   ->prefix('JD')
                   ->maxValue(10000),
                ]),


           ]);
       }
    public static function table(Table $table): Table
     {
        return $table
        ->columns([
            TextColumn::make('class_name')
                ->searchable()
                ->sortable()
                 ->weight('medium'),
            TextColumn::make('day_of_week')
                ->formatStateUsing(fn ($state) => ['Sun','Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][$state])
                ->sortable(),
           TextColumn::make('start_time')
                ->time('h:i A') 
                 ->sortable(),
             TextColumn::make('end_time')
               ->time('h:i A')
              ->sortable(),
            TextColumn::make('instructor.name')
                ->label('Instructor')
               ->searchable()
              ->sortable(),
             TextColumn::make('capacity')
                ->sortable(),
                TextColumn::make('price_per_month'),

        ])
          ->filters([
            SelectFilter::make('instructor_id')
                ->label('Instructor')
                ->relationship('instructor', 'name')
                ->preload(),
           ])
           ->actions([
               // ViewAction::make(),
               Tables\Actions\EditAction::make()
               ->iconButton() 
                ->tooltip('Edit class'),
               ])
           ->bulkActions([
              // ...
              Tables\Actions\DeleteBulkAction::make()
                ->requiresConfirmation() 
          ]);
      }

           public static function getRelations(): array
         {
            return [
                // Add the relation manager
                 RelationManagers\StudentsRelationManager::class,
                //  RelationManagers\SessionsRelationManager::class,

            ];
           }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClassModels::route('/'),
            'create' => Pages\CreateClassModel::route('/create'),
            'edit' => Pages\EditClassModel::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'info';
    }
}