<?php

namespace App\Filament\Resources\ClassModelResource\RelationManagers;
use App\Models\ClassEnrollment;
use App\Models\Student;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;

class StudentsRelationManager extends RelationManager
{
    protected static string $relationship = 'students';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('student_id')
                    ->label('Student')
                    ->options(Student::all()->pluck('english_name', 'id'))
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\DatePicker::make('enrollment_date')
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->default(true),
            ]);
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return ClassEnrollment::query()->where('class_id', $this->ownerRecord->id); // Adjust as necessary
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('english_name')
            ->query($this->getTableQuery()->with('student'))
            ->columns([
                Tables\Columns\TextColumn::make('student.english_name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('enrollment_date')->date()->sortable(),
                Tables\Columns\IconColumn::make('is_active')->boolean()->sortable(),
            ])
            ->filters([
                // ...
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Enroll Student')
                    ->using(function (array $data): Model {
                        $student = Student::findOrFail($data['student_id']);
                        $classPrice = $this->ownerRecord->price_per_month;

                        return DB::transaction(function () use ($data, $student, $classPrice) {
                            // Update student balance
                            $student->balance -= $classPrice;
                            $student->save();

                            // Create the enrollment only
                            $enrollment = ClassEnrollment::create([
                                'student_id' => $data['student_id'],
                                'class_id' => $this->ownerRecord->id,
                                'enrollment_date' => now(),
                                'is_active' => true,
                                'user_id' => auth()->id(),
                            ]);

                            Notification::make()
                                ->success()
                                ->title('Student Enrolled Successfully')
                                ->body("New balance: JD {$student->balance}")
                                ->send();

                            return $enrollment;
                        });
                    }),
            ])
            ->actions([
            ])
            ->bulkActions([
                
            ])
            ->emptyStateActions([

            ]);
    }
}