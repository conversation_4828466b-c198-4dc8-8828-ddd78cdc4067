<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
    Schema::create('class_enrollments', function (Blueprint $table) {
      $table->id();
      $table->foreignId('student_id')->constrained()->onDelete('cascade');
      $table->foreignId('class_id')->constrained('class_models')->onDelete('cascade'); // Reference class_models
      $table->date('enrollment_date');
      $table->boolean('is_active')->default(true);
      $table->timestamps();

        // Add a unique constraint to prevent duplicate enrollments
        $table->unique(['student_id', 'class_id']);
      });
    }

    public function down(): void
    {
        Schema::dropIfExists('class_enrollments');
    }
};