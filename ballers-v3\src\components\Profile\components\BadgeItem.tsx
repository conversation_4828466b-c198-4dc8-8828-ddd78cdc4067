import { Badge as BadgeIcon } from 'lucide-react';
import { Badge } from './ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';

interface BadgeItemProps {
  name: string;
  description: string;
  earnedDate: string;
  iconUrl?: string;
}

const BadgeItem = ({ name, description, earnedDate, iconUrl }: BadgeItemProps) => {
  return (
    <div className="flex gap-3 items-start border-b pb-3 last:border-0">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="relative flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-bball-gold/20 flex items-center justify-center">
                {iconUrl ? (
                  <img src={iconUrl} alt={name} className="h-6 w-6 badge-icon" />
                ) : (
                  <BadgeIcon className="h-6 w-6 text-bball-gold badge-icon" />
                )}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Earned on {earnedDate}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium truncate text-bball-dark-purple">{name}</h4>
          <Badge variant="outline" className="text-xs bg-bball-gold/10 text-bball-gold border-bball-gold/20">
            {earnedDate}
          </Badge>
        </div>
        <p className="text-xs text-muted-foreground line-clamp-2">{description}</p>
      </div>
    </div>
  );
};

export default BadgeItem;
