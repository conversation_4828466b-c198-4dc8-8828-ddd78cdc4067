import { Progress } from "./ui/progress";

interface AttendanceStatsProps {
  attended: number;
  missed: number;
  rate: number;
}

const AttendanceStats = ({ attended, missed, rate }: AttendanceStatsProps) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-1">
        <div className="space-y-1">
          <h4 className="text-sm font-medium leading-none">Attendance Rate</h4>
          <p className="text-sm text-muted-foreground">
            {attended} attended, {missed} missed
          </p>
        </div>
        <div className="text-xl font-bold text-bball-gold">{rate}%</div>
      </div>
      <Progress value={rate} className="h-2 bg-bball-light-gray [&>*]:bg-bball-gold" />
      
      <div className="grid grid-cols-3 gap-2 pt-2">
        <div className="bg-bball-light-gray rounded-md p-3 text-center">
          <div className="text-xl font-bold text-bball-dark-purple">{attended}</div>
          <div className="text-xs text-muted-foreground">Attended</div>
        </div>
        <div className="bg-bball-light-gray rounded-md p-3 text-center">
          <div className="text-xl font-bold text-bball-dark-purple">{missed}</div>
          <div className="text-xs text-muted-foreground">Missed</div>
        </div>
        <div className="bg-bball-gold/20 rounded-md p-3 text-center">
          <div className="text-xl font-bold text-bball-gold">{attended + missed}</div>
          <div className="text-xs text-muted-foreground">Total</div>
        </div>
      </div>
    </div>
  );
};

export default AttendanceStats;
