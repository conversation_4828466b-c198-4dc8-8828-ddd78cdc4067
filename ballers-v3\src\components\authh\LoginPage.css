.auth-wrapper {
  width: 100%;
  min-height: 100vh;
  font-family: 'Poppins', sans-serif;
  background-color: #fff;
}

.auth-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Left Side: Login Form */
.auth-form-section {
  width: 50%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  z-index: 2;
}

.auth-logo {
  font-size: 28px;
  color: #ef6c00;
  margin-bottom: 10px;
  font-weight: bold;
  text-align: center;
  width: 100%;
}

.auth-form-section h2 {
  font-size: 22px;
  color: #333;
  margin-bottom: 10px;
}

.auth-form-section p {
  font-size: 14px;
  color: #777;
  margin-bottom: 20px;
  text-align: center;
}

.auth-form-section input {
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 15px;
}

.auth-form-section button {
  width: 100%;
  padding: 12px;
  background: linear-gradient(to right, #ef6c00, #f57c00);
  color: white;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.auth-form-section button:hover {
  background: linear-gradient(to right, #e65100, #f4511e);
}

.auth-forgot-password {
  margin-top: 15px;
}

.auth-forgot-password a {
  color: #ef6c00;
  text-decoration: none;
  font-size: 14px;
}

.auth-error {
  color: red;
  font-size: 14px;
  margin-top: 10px;
}

/* Right Side: Image Carousel */
.auth-image-section {
  width: 50%;
  position: relative;
  background-size: cover;
  background-position: center;
  overflow: hidden;
}

.auth-carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 1s ease-in-out, transform 1s ease-in-out;
  z-index: 0;
}

.auth-carousel-slide.active {
  opacity: 1;
  visibility: visible;
  z-index: 1;
  animation: fadeInScale 1s ease forwards;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.auth-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.auth-caption {
  position: absolute;
  bottom: 40px;
  left: 30px;
  color: white;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.6);
  z-index: 2;
  animation: captionUp 0.8s ease forwards;
}

@keyframes captionUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.auth-dots {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
  z-index: 2;
}

.auth-dot {
  width: 10px;
  height: 10px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.auth-dot.active,
.auth-dot:hover {
  background-color: white;
}

/* Responsive Design */
@media screen and (max-width: 900px) {
  .auth-container {
    flex-direction: column;
  }

  .auth-form-section {
    width: 100%;
    height: auto;
    z-index: 2;
    padding: 30px 20px;
  }

  .auth-image-section {
    display: none; /* Hide on mobile */
  }

  .auth-logo {
    font-size: 24px;
  }

  .auth-form-section h2 {
    font-size: 20px;
  }

  .auth-form-section p {
    font-size: 14px;
  }
}