import { useState } from 'react';
import { Dialog, DialogContent } from '../../../components/ui/dialog';

interface MediaItem {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnail: string;
  title: string;
  date: string;
}

interface MediaGalleryProps {
  items: MediaItem[];
}

const MediaGallery = ({ items }: MediaGalleryProps) => {
  const [selectedItem, setSelectedItem] = useState<MediaItem | null>(null);

  return (
    <>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
        {items.map((item) => (
          <div
            key={item.id}
            className="relative aspect-square rounded-md overflow-hidden media-gallery-item"
            onClick={() => setSelectedItem(item)}
          >
            <img 
              src={item.thumbnail} 
              alt={item.title} 
              className="w-full h-full object-cover"
            />
            {item.type === 'video' && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-10 h-10 rounded-full bg-black/50 flex items-center justify-center">
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    className="text-white"
                  >
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                  </svg>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      <Dialog open={!!selectedItem} onOpenChange={(open: boolean) => !open && setSelectedItem(null)}>
        <DialogContent className="sm:max-w-3xl">
          <div>
            {selectedItem?.type === 'image' ? (
              <img 
                src={selectedItem.url} 
                alt={selectedItem.title} 
                className="w-full h-auto rounded-md"
              />
            ) : (
              <video
                src={selectedItem?.url}
                controls
                className="w-full h-auto rounded-md"
              />
            )}
            <div className="mt-2">
              <h3 className="text-lg font-medium">{selectedItem?.title}</h3>
              <p className="text-sm text-muted-foreground">{selectedItem?.date}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MediaGallery;
