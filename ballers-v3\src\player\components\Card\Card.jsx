import React from 'react';
import './Card.css';

const Card = ({ 
  children, 
  className = '', 
  elevation = 'md',
  onClick,
  ...props 
}) => {
  const elevationClass = `card--${elevation}`;
  
  return (
    <div 
      className={`card ${elevationClass} ${className}`} 
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

export const CardHeader = ({ children, className = '', ...props }) => {
  return (
    <div className={`card__header ${className}`} {...props}>
      {children}
    </div>
  );
};

export const CardContent = ({ children, className = '', ...props }) => {
  return (
    <div className={`card__content ${className}`} {...props}>
      {children}
    </div>
  );
};

export const CardFooter = ({ children, className = '', ...props }) => {
  return (
    <div className={`card__footer ${className}`} {...props}>
      {children}
    </div>
  );
};

export const CardMedia = ({ 
  src, 
  alt = '', 
  className = '', 
  aspectRatio = '16/9',
  ...props 
}) => {
  const style = { 
    '--aspect-ratio': aspectRatio 
  };
  
  return (
    <div className={`card__media ${className}`} style={style} {...props}>
      <img src={src} alt={alt} />
    </div>
  );
};

export default Card;