<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'profile_photo' => $this->profile_photo,
            'arabic_name' => $this->arabic_name,
            'english_name' => $this->english_name,
            'gender' => $this->gender,
            'dob' => $this->dob,
            'email' => $this->email,
            'age' => $this->age,
            'parent_number' => $this->parent_number,
            'level_of_player' => $this->level_of_player,
            'position' => $this->position,
            'category' => $this->category,
            'weight' => $this->weight,
            'height' => $this->height,
            'school_name' => $this->school_name,
            'instagram' => $this->instagram,
            'facebook' => $this->facebook,
            'balance' => $this->balance,
            'active' => $this->active,
            'registration_date' => $this->registration_date,
            'classes' => ClassResource::collection($this->whenLoaded('classes')),
            'attendances' => AttendanceResource::collection($this->whenLoaded('attendances')),
            'payments' => PaymentResource::collection($this->whenLoaded('payments')),
        ];
    }
}
