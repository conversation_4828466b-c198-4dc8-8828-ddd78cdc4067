import React from 'react';
import './Grid.css';

const Grid = ({ 
  children, 
  columns = { 
    xs: 1, 
    sm: 2, 
    md: 3, 
    lg: 4 
  }, 
  gap = 'md',
  className = '', 
  ...props 
}) => {
  // Convert columns object to class names
  const columnClasses = Object.entries(columns)
    .map(([breakpoint, value]) => {
      if (breakpoint === 'xs') {
        return `grid--cols-${value}`;
      }
      return `${breakpoint}:grid--cols-${value}`;
    })
    .join(' ');
  
  const gapClass = `grid--gap-${gap}`;
  
  return (
    <div 
      className={`grid ${columnClasses} ${gapClass} ${className}`} 
      {...props}
    >
      {children}
    </div>
  );
};

export default Grid;