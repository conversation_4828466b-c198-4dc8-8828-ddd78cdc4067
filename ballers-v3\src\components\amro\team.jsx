import React from 'react';
import './team.css'; // You'll need to create this file

const Team = () => {
  const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Founder and Chief Operations Officer",
      img: "https://images.pexels.com/photos/2811089/pexels-photo-2811089.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=11"
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Founder and Chief Executive Officer",
      img: "https://images.pexels.com/photos/91227/pexels-photo-91227.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Chief Process and Innovation Officer",
      img: "https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    },
    {
      id: 4,
      name: "<PERSON>",
      position: "Chief Sales Officer",
      img: "https://images.pexels.com/photos/2216607/pexels-photo-2216607.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    },
    {
      id: 5,
      name: "Artur Dichter",
      position: "Chief Financial Officer",
      img: "https://images.pexels.com/photos/1043473/pexels-photo-1043473.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    },
    {
      id: 6,
      name: "Oliver Rohlsson",
      position: "Chief Technical Officer",
      img: "https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    },
    {
      id: 7,
      name: "James Hedge",
      position: "Chief Legal Officer",
      img: "https://images.pexels.com/photos/262391/pexels-photo-262391.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    },
    {
      id: 8,
      name: "Eve Johnsson",
      position: "HR Manager",
      img: "https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
    }
  ];

  return (
    <div className='sectionnnn'>
      <div className="card-top">
        {teamMembers.slice(0, 2).map(member => (
          <div className="card" key={member.id}>
            <img src={member.img} alt={member.name} />
            <h2>{member.name}</h2>
            <p>{member.position}</p>
          </div>
        ))}
      </div>
      {teamMembers.slice(2).map(member => (
        <div className="card" key={member.id}>
          <img src={member.img} alt={member.name} />
          <h2>{member.name}</h2>
          <p>{member.position}</p>
        </div>
      ))}
    </div>
  );
};

export default Team;