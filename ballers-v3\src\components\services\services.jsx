import React from 'react';
import { 
  Fa<PERSON>ser, 
  FaTrophy, 
  FaHeart, 
  FaBasketballBall, 
  FaCalendarAlt, 
  FaLaptop,
  FaArrowDown
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import './services.css';

function Example() {
  const { t } = useTranslation();
  return (
    <div>
      <h2 className="speacial-heading">{t('services')}</h2>
      <p>{t('tagline')}</p>
      <section className="services">
        <div className="container_services">
          <div className="serviceBox">
            <div className="icon" style={{ "--i": "#BDE4F4" }}>
              <FaTrophy
                color={'#a69b9b'}
                size={40}
              />
            </div>
            <div className="content_services">
              <h2>{t('deliverTitle')}</h2>
              <p>{t('deliverContent')}</p>
            </div>
          </div>
          <div className="serviceBox">
            <div className="icon" style={{ "--i": "#404969" }}>
              <FaBasketballBall
                color={'#a69b9b'}
                size={40}
              />
            </div>
            <div className="content_services">
              <h2>{t('PaymentTitle')}</h2>
              <p>{t('PaymentContent')}</p>
            </div>
          </div>
          <div className="serviceBox">
            <div className="icon" style={{ "--i": "#DC552C" }}>
              <FaUser
                color={'#a69b9b'}
                size={40}
              />
            </div>
            <div className="content_services">
              <h2>{t('StoresTitle')}</h2>
              <p>{t('StoresContent')}</p>
            </div>
          </div>
          <div className="serviceBox">
            <div className="icon" style={{ "--i": "#404969" }}>
              <FaCalendarAlt
                color={'#a69b9b'}
                size={40}
              />
            </div>
            <div className="content_services">
              <h2>{t('DigitalTitle')}</h2>
              <p>{t('DigitalContent')}</p>
            </div>
          </div>
          <div className="serviceBox">
            <div className="icon" style={{ "--i": "#DC552C" }}>
              <FaHeart
                color={'#a69b9b'}
                size={40}
              />
            </div>
            <div className="content_services">
              <h2>{t('CustomerTitle')}</h2>
              <p>{t('CustomerContent')}</p>
            </div>
          </div>
          <div className="serviceBox">
            <div className="icon" style={{ "--i": "#BDE4F4" }}>
              <FaLaptop
                color={'#a69b9b'}
                size={40}
              />
            </div>
            <div className="content_services">
              <h2>{t('ReturnsTitle')}</h2>
              <p>{t('ReturnsContent')}</p>
            </div>
          </div>
        </div>
      </section>

      <section className='After-services'>
        <h2 id='testggg'>{t('After-services')}</h2>
        <FaArrowDown
          color={'#404969'}
                size={40}
        />
      </section>
    </div>
  );
}

export default Example;