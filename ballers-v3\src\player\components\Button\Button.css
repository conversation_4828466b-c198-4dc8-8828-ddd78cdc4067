.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast) var(--transition-timing);
  cursor: pointer;
  text-align: center;
  border: none;
  position: relative;
  overflow: hidden;
}

/* Button variants */
.button--primary {
  background-color: var(--color-primary-600);
  color: white;
}

.button--primary:hover {
  background-color: var(--color-primary-700);
}

.button--primary:active {
  background-color: var(--color-primary-800);
}

.button--secondary {
  background-color: var(--color-secondary-600);
  color: white;
}

.button--secondary:hover {
  background-color: var(--color-secondary-700);
}

.button--secondary:active {
  background-color: var(--color-secondary-800);
}

.button--accent {
  background-color: var(--color-accent-600);
  color: white;
}

.button--accent:hover {
  background-color: var(--color-accent-700);
}

.button--accent:active {
  background-color: var(--color-accent-800);
}

.button--outline {
  background-color: transparent;
  border: 1px solid var(--color-neutral-300);
  color: var(--color-neutral-700);
}

.button--outline:hover {
  border-color: var(--color-neutral-400);
  color: var(--color-neutral-900);
  background-color: var(--color-neutral-50);
}

.button--outline:active {
  background-color: var(--color-neutral-100);
}

.button--ghost {
  background-color: transparent;
  color: var(--color-primary-600);
}

.button--ghost:hover {
  background-color: var(--color-primary-50);
}

.button--ghost:active {
  background-color: var(--color-primary-100);
}

/* Button sizes */
.button--sm {
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
}

.button--md {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
}

.button--lg {
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-base);
}

/* Disabled state */
.button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button--disabled:hover {
  opacity: 0.5;
}

/* Ripple effect */
.button::after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.3s, opacity 0.5s;
}

.button:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* With icon */
.button-icon {
  margin-right: var(--space-2);
  display: inline-flex;
  align-items: center;
}

.button-icon-right {
  margin-left: var(--space-2);
  margin-right: 0;
}