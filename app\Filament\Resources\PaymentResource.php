<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentResource\Pages;
use App\Models\Payment;
use Filament\Forms;
use Filament\Tables;
use Filament\Resources\Resource;
use Filament\Support\Colors\Color;
use App\Services\PaymentService;
// use Filament\Tables\Columns\Layout\Card;

class PaymentResource extends Resource
{
    protected static ?string $model = Payment::class;
    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $navigationGroup = 'Financial Management';
    protected static ?string $modelLabel = 'Payment';
    protected static ?string $pluralModelLabel = 'Payments';
    protected static ?int $navigationSort = 5;

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Payment Details')
                            ->description('Record new payment')
                            ->icon('heroicon-o-credit-card')
                            ->columns(2)
                            ->schema([
                                Forms\Components\Select::make('class_id')
                                    ->label('Class')
                                    ->relationship('classModel', 'class_name')
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $class = \App\Models\ClassModel::find($state);
                                            $set('amount', $class?->price_per_month ?? 0);
                                            // Clear student selection when class changes
                                            $set('student_id', null);
                                        }
                                    }),

                                Forms\Components\Select::make('student_id')
                                    ->label('Student')
                                    ->options(function (callable $get) {
                                        $classId = $get('class_id');
                                        if (!$classId) return [];
                                        
                                        return \App\Models\ClassModel::find($classId)
                                            ?->students()
                                            ->where('is_active', true)
                                            ->get()
                                            ->mapWithKeys(function ($student) {
                                                return [$student->id => $student->english_name];
                                            }) ?? [];
                                    })
                                    ->searchable()
                                    ->required()
                                    ->live()
                                    ->hidden(fn ($get) => !$get('class_id')),

                                Forms\Components\TextInput::make('amount')
                                    ->required()
                                    ->numeric()
                                    ->prefix('JD')
                                    ->minValue(0)
                                    ->step(0.01)
                                    ->dehydrated(),

                                Forms\Components\Select::make('payment_method')
                                    ->options([
                                        'cash' => 'Cash',
                                        'card' => 'Card Payment',
                                        'transfer' => 'Bank Transfer',
                                    ])
                                    ->required()
                                    ->native(false),

                                Forms\Components\DatePicker::make('payment_date')
                                    ->default(now())
                                    ->required(),
                            ]),

                        Forms\Components\Section::make('Payment Notes')
                            ->schema([
                                Forms\Components\Textarea::make('notes')
                                    ->rows(3),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Payment Summary')
                            ->schema([
                                Forms\Components\Placeholder::make('student_balance')
                                    ->label('Current Balance')
                                    ->content(function ($get) {
                                        $studentId = $get('student_id');
                                        if (!$studentId) return 'JD 0.00';
                                        
                                        $student = \App\Models\Student::find($studentId);
                                        return 'JD ' . number_format($student?->balance ?? 0, 2);
                                    }),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student.english_name')
                    ->label('Student')
                    ->formatStateUsing(fn ($record) => $record->student->english_name)
                    ->sortable()
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('classModel.class_name')
                    ->label('Class')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('amount')
                    ->prefix('JD ')
                    ->numeric(2, ',') // Fixed syntax for numeric formatting
                    ->sortable()
                    ->alignment('right'),

                Tables\Columns\TextColumn::make('payment_date')
                    ->date('d/m/Y')
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('payment_method')
                    ->colors([
                        'success' => 'cash',
                        'primary' => 'card',
                        'warning' => 'transfer',
                        'danger' => 'other',
                    ]),
            ])
            ->defaultSort('payment_date', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'card' => 'Card',
                        'transfer' => 'Transfer',
                        'other' => 'Other',
                    ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayments::route('/'),
            'create' => Pages\CreatePayment::route('/create'),
            'edit' => Pages\EditPayment::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return number_format(static::getModel()::sum('amount'), 2) . ' JD';
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'success';
    }
}