.container-aboutf {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 5rem;
    text-align: justify;
}
.container-aboutf1 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 100%;
    height: auto;
  }
  
  .responsive-video-a {
    max-width: 100%;
    height: auto;
  }
  
.container-aboutf h5 {
    font-size: 1.3rem;
    color: rgb(39, 147, 241);

}
.container-aboutf p {
    font-size: 0.5rem;
    color: aliceblue;
}


.ctaa-about {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    text-decoration: none;
    background-color: #DC552C;
    /*Adjustthecolortoyourbranding*/color: #ffffff;
    /*Adjustthetextcolortoensurereadability*/border-radius: 5px;
    margin-top: 4rem;
    -webkit-transition: background-color 0.3s ease;
    -moz-transition: background-color 0.3s ease;
    -ms-transition: background-color 0.3s ease;
    -o-transition: background-color 0.3s ease;
    transition: background-color 0.3s ease;
}
