
.main-faq {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}
.faq-container {
    margin: 0 4rem;
    max-width: 47rem;
    display: flex;
    flex-direction: column;
    gap: 20px;
}
.faq-container .speacial-heading {
    font-size: 9rem;
}
.faq-container .tab {
    position: relative;
    /* background-color: #fff; */
    padding: 0 20px 20px;
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    color: #ffffff;
}
.faq-container .tab input {
    appearance: none;
}
.faq-container .tab label {
    display: flex;
    align-items: center;
    cursor: pointer;
}
.faq-container .tab label::after {
    content: '';
    position: absolute;
    right: 20px;
    top: 50%;
    width: 40px;
    height: 40px;
    background: url('../images//ballers_logo.png') no-repeat center center;
    background-size: cover;
    -webkit-transition: transform 1s;
    -moz-transition: transform 1s;
    -ms-transition: transform 1s;
    -o-transition: transform 1s;
    transition: transform 1s;
    cursor: pointer;
    -webkit-transform: t;
    -moz-transform: t;
    -ms-transform: t;
    -o-transform: t;
    transform: translateY(-2%);
    transform: t;
}

.faq-container .tab:hover label::after {
    -webkit-transform: translateY(-5%) rotate(175deg);
    -moz-transform: translateY(-5%) rotate(175deg);
    -ms-transform: translateY(-5%) rotate(175deg);
    -o-transform: translateY(-5%) rotate(175deg);
    transform: translateY(-5%) rotate(175deg);
}

.faq-container .tab input:checked ~ label::after {
    -webkit-transform:rotate(135deg);
    -moz-transform:rotate(135deg);
    -ms-transform:rotate(135deg);
    -o-transform:rotate(135deg);
    transform:rotate(135deg);
    color: #debeb5;
}
.faq-container .tab input:checked ~ label h2 {

    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: rgba(255, 255, 255, 0.2);
    font-size: 8em;
    justify-content: flex-end;
    padding: 20px;
}

.faq-container .tab label h2 {
    width: 40px;
    height: 40px;
    background-color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.25rem;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    margin-right: 10px;
}
.faq-container .tab label h3 {
    color: green;
}
.faq-container .tab:nth-child(2) label h2 {
    background: linear-gradient(135deg, #404969, #4a516c);
}
.faq-container .tab:nth-child(4) label h2 {
    background: linear-gradient(135deg, #404969, #4a516c);
}
.faq-container .tab:nth-child(3) label h2 {
    background: linear-gradient(135deg, #DC552C, rgb(158, 109, 95));
}
.faq-container .tab:nth-child(5) label h2 {
    background: linear-gradient(135deg, #DC552C, rgb(158, 109, 95));
}

.faq-container .tab input:checked ~ label h3 {
    background-color: #fff;
    padding: 2px 10px;
    color: #777777;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}
.faq-container .tab label h3 {
    position: relative;
    font-weight: 500;
    color: #b0b0b0;
    z-index: 10;
    font-size: 1.1rem;

}
.faq-container .tab .content {
    max-height: 0;
    -webkit-transition: 1s;
    -moz-transition: 1s;
    -ms-transition: 1s;
    -o-transition: 1s;
    transition: 1s;
    overflow: hidden;
}
.faq-container .tab input:checked ~ .content{
    max-height: 100vh;
}
.faq-container .tab .content p {
    position: relative;
    padding: 10px 0;
    color: #333;
    z-index: 10;
}
.faq-container .tab input:checked ~ .content p {
    color: #fff;
}