:root {
  /* Primary Colors */
  --color-primary-50: #fef3f1;
  --color-primary-100: #fde4dd;
  --color-primary-200: #fbc9bb;
  --color-primary-300: #f9ae99;
  --color-primary-400: #f79377;
  --color-primary-500: #dc552c; 
  --color-primary-600: #c74d27;
  --color-primary-700: #b24522;
  --color-primary-800: #9e3c1d;
  --color-primary-900: #893418;

  /* Neutral Colors */
  --color-neutral-50: #F8F9FA;
  --color-neutral-100: #E9ECEF;
  --color-neutral-200: #DEE2E6;
  --color-neutral-300: #CED4DA;
  --color-neutral-400: #ADB5BD;
  --color-neutral-500: #6C757D;
  --color-neutral-600: #495057;
  --color-neutral-700: #343A40;
  --color-neutral-800: #212529;
  --color-neutral-900: #121416;

  /* Status Colors */
  --color-success-50: #E8F5E9;
  --color-success-500: #4CAF50;
  --color-success-600: #2E7D32;

  --color-warning-50: #FFF3E0;
  --color-warning-500: #FF9800;
  --color-warning-600: #F57C00;

  /* Typography */
  --font-family-primary: system-ui, -apple-system, "Segoe UI", Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Borders */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}