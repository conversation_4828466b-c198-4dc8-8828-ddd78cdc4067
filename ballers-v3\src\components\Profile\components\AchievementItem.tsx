import { Medal } from 'lucide-react';

interface AchievementItemProps {
  title: string;
  date: string;
  description: string;
  medal?: 'gold' | 'silver' | 'bronze' | null;
}

const AchievementItem = ({ title, date, description, medal }: AchievementItemProps) => {
  const getMedalColor = () => {
    switch (medal) {
      case 'gold':
        return 'text-bball-gold bg-bball-gold/10 border-bball-gold/20';
      case 'silver':
        return 'text-gray-400 bg-gray-100 border-gray-200';
      case 'bronze':
        return 'text-amber-700 bg-amber-50 border-amber-100';
      default:
        return 'text-bball-purple bg-bball-purple/10 border-bball-purple/20';
    }
  };

  return (
    <div className="flex gap-3 items-start border-b pb-3 last:border-0">
      <div className={`h-10 w-10 rounded-full flex items-center justify-center ${medal ? getMedalColor() : 'bg-bball-light-gray'}`}>
        {medal ? (
          <Medal className="h-5 w-5" />
        ) : (
          <div className="h-5 w-5 rounded-full bg-bball-purple/30" />
        )}
      </div>
      <div>
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium text-bball-dark-purple">{title}</h4>
          <span className="text-xs text-muted-foreground">{date}</span>
        </div>
        <p className="text-xs text-muted-foreground mt-0.5">{description}</p>
      </div>
    </div>
  );
};

export default AchievementItem;
