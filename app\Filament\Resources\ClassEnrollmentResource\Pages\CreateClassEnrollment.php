<?php

namespace App\Filament\Resources\ClassEnrollmentResource\Pages;

use App\Filament\Resources\ClassEnrollmentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;

class CreateClassEnrollment extends CreateRecord
{
    protected static string $resource = ClassEnrollmentResource::class;

    protected function afterCreate(): void
    {
        $student = $this->record->student;
        $class = $this->record->class;

        DB::transaction(function () use ($student, $class) {
            // Update student balance only
            $student->balance -= $class->price_per_month;
            $student->save();

            Notification::make()
                ->success()
                ->title('Student Enrolled Successfully')
                ->body("New balance: JD {$student->balance}")
                ->send();
        });
    }
}
