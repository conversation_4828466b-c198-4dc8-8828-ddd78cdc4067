.card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: var(--space-4);
}

.card__header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-neutral-100);
}

.card__title {
  color: #DC552C;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
}

.card__subtitle {
  color: var(--color-neutral-600);
  font-size: var(--font-size-sm);
}

.card__content {
  padding: var(--space-4);
}

.card__stat {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: var(--space-4);
  background-color: #F8F9FA;
  border-radius: 8px;
  min-width: 100px;
  text-align: center;
}

.card__stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: #DC552C;
  margin-bottom: var(--space-1);
}

.card__stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
}

.card__progress {
  height: 8px;
  background-color: #E9ECEF;
  border-radius: 4px;
  margin: var(--space-2) 0;
  overflow: hidden;
}

.card__progress-bar {
  height: 100%;
  background-color: #FFD700;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.card__payment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-bottom: 1px solid var(--color-neutral-100);
}

.card__payment:last-child {
  border-bottom: none;
}

.card__payment-status {
  padding: 4px 12px;
  border-radius: 999px;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.card__payment-status--paid {
  background-color: #E8F5E9;
  color: #2E7D32;
}

.card__payment-status--pending {
  background-color: #FFF3E0;
  color: #F57C00;
}

.card__footer {
  padding: var(--space-4);
  background-color: #F8F9FA;
  border-top: 1px solid var(--color-neutral-100);
}