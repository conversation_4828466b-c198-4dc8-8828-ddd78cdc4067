# Ballers API Documentation

## Base URL
`http://ballers.test/api`

## Authentication

### Login
- **URL:** `/login`
- **Method:** `POST`
- **Headers:** 
  ```
  Accept: application/json
  Content-Type: application/json
  ```
- **Body:**
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Success Response:**
  ```json
  {
    "status": "success",
    "data": {
      "token": "1|abcdef123456...",
      "token_type": "Bearer",
      "student": {
        "id": 1,
        "email": "<EMAIL>",
        "english_name": "<PERSON>",
        "arabic_name": "جون دو",
        "profile_photo": "student-photos/photo.jpg",
        "gender": "Male",
        ""
        "age": "23",
        "level_of_player": "Intermediate",
        "position": "Forward",
        "category": "U23"
      }
    }
  }
  ```
- **Error Response:**
  ```json
  {
    "status": "error",
    "message": "Validation failed",
    "errors": {
      "email": ["The provided credentials are incorrect or the account is inactive."]
    }
  }
  ```

## Protected Routes
All protected endpoints require authentication header:
```
Authorization: Bearer {token}
Accept: application/json
```

### Get Profile
- **URL:** `/profile`
- **Method:** `GET`
- **Success Response:**
  ```json
  {
    "student": {
      "id": 1,
      "email": "<EMAIL>",
      "english_name": "John Doe",
      "arabic_name": "جون دو",
      "profile_photo": "student-photos/photo.jpg",
      "gender": "Male",
      "age": 23,
      "dob": "2000-01-01",
      "parent_number": "+**********",
      "weight": 75,
      "height": 180,
      "school_name": "International School",
      "level_of_player": "Intermediate",
      "position": "Forward",
      "category": "U23",
      "balance": "100.00",
      "instagram": "john_doe",
      "facebook": "johndoe",
      "registration_date": "2024-01-01"
    }
  }
  ```

### Get Classes
- **URL:** `/classes`
- **Method:** `GET`
- **Success Response:**
  ```json
  {
    "classes": [
      {
        "id": 1,
        "name": "Advanced Training",
        "instructor_name": "Coach Smith",
        "schedule": "Monday 16:00-18:00",
        "description": "Advanced training session",
        "price_per_month": "100.00"
      }
    ]
  }
  ```

### Get Attendances
- **URL:** `/attendances`
- **Method:** `GET`
- **Description:** Returns last 30 attendances
- **Success Response:**
  ```json
  {
    "attendances": [
      {
        "id": 1,
        "class_name": "Advanced Training",
        "date": "2024-02-10",
        "attended": true
      }
    ]
  }
  ```

### Get Payments
- **URL:** `/payments`
- **Method:** `GET`
- **Description:** Returns last 20 payments
- **Success Response:**
  ```json
  {
    "payments": [
      {
        "id": 1,
        "amount": "100.00",
        "class_name": "Advanced Training",
        "payment_date": "2024-02-10",
        "payment_method": "cash",
        "status": "paid"
      }
    ]
  }
  ```

### Logout
- **URL:** `/logout`
- **Method:** `POST`
- **Success Response:**
  ```json
  {
    "status": "success",
    "message": "Successfully logged out"
  }
  ```

## Testing with Postman

1. Create a new collection "Ballers API"

2. Set environment variables:
   - `base_url`: Your API base URL
   - `token`: Will be filled after login

3. Test Flow:
   1. Login:
      ```http
      POST {{base_url}}/login
      Content-Type: application/json
      {
        "email": "<EMAIL>",
        "password": "password123"
      }
      ```
   2. Copy token from response
   3. Set token in environment
   4. Test other endpoints using the token
