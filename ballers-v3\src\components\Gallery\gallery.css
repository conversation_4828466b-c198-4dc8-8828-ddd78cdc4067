.gallery-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.gallery-row {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  margin-bottom: 10px;
  height: 400px; /* Max height for desktop */
}

.gallery-row img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s;
}

.gallery-row img:hover {
  transform: scale(1.02);
}

/* Shared image styles */
.img-large,
.img-small,
.half,
.quarter,
.full,
.fifth,
.third {
  height: 100%;
  overflow: hidden;
}

/* Desktop: Custom widths */
.img-large {
  flex: 2;
}
.img-small {
  flex: 1;
}
.half, .quarter, .full, .fifth, .third {
  flex: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .gallery-row {
    flex-wrap: wrap;
    height: auto;
  }

  .img-large,
  .img-small,
  .half,
  .quarter,
  .full,
  .fifth,
  .third {
    flex: 1 1 calc(50% - 10px); /* 2 images per row */
    height: 200px;
  }

  .gallery-row img {
    height: 100%;
  }
}

@media (max-width: 480px) {
  .img-large,
  .img-small,
  .half,
  .quarter,
  .full,
  .fifth,
  .third {
    flex: 1 1 100%; /* 1 image per row */
    height: 180px;
  }
}

.gallery-row.row-4 {
  display: flex;
  flex-wrap: wrap;
}

.gallery-row.row-4 .half {
  width: 50%;
  padding: 4px;
}

.gallery-row.row-4 .half img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  max-height: 400px;
}
